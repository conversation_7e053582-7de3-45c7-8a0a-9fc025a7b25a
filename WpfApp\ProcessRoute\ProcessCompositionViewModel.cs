// 引用系统基础类库
using System;
// 引用可观察集合类库
using System.Collections.ObjectModel;
// 引用属性变更通知接口
using System.ComponentModel;
// 引用HTTP客户端类库
using System.Net.Http;
// 引用运行时编译器服务
using System.Runtime.CompilerServices;
// 引用JSON序列化类库
using System.Text.Json;
// 引用JSON序列化注解类库
using System.Text.Json.Serialization;
// 引用异步任务类库
using System.Threading.Tasks;
// 引用WPF窗口类库
using System.Windows;
// 引用WPF输入类库
using System.Windows.Input;
// 引用通用命令类库
using WpfApp.Common;
// 引用泛型集合类库
using System.Collections.Generic;
// 引用LINQ查询类库
using System.Linq;

// 定义工艺路线命名空间
namespace WpfApp.ProcessRoute
{
    /// <summary>
    /// 工艺路线组成视图模型类
    /// 实现INotifyPropertyChanged接口，支持数据绑定和属性变更通知
    /// 负责处理工艺路线组成页面的业务逻辑和数据操作
    /// </summary>
    public class ProcessCompositionViewModel : INotifyPropertyChanged
    {
        // HTTP客户端实例，用于发送API请求
        private readonly HttpClient _httpClient;
        // 工艺路线组成数据集合的私有字段
        private ObservableCollection<ProcessComposition> _processCompositions;
        // 工艺路线ID筛选条件的私有字段
        private string _processRouteIdFilter;
        // 当前页码的私有字段，默认为第1页
        private int _currentPage = 1;
        // 每页显示数量的私有字段，默认为10条
        private int _pageSize = 10;
        // 总记录数的私有字段
        private int _totalCount;
        // 总页数的私有字段
        private int _totalPages;

        /// <summary>
        /// 构造函数 - 初始化工艺路线组成视图模型
        /// </summary>
        public ProcessCompositionViewModel()
        {
            // 创建HTTP客户端实例
            _httpClient = new HttpClient();
            // 初始化工艺路线组成数据集合
            ProcessCompositions = new ObservableCollection<ProcessComposition>();

            // 初始化各种命令
            // 搜索命令 - 绑定到LoadDataAsync异步方法
            SearchCommand = new RelayCommand(async _ => await LoadDataAsync());
            // 清空命令 - 绑定到ClearFilter方法
            ClearCommand = new RelayCommand(_ => ClearFilter());
            // 刷新命令 - 绑定到LoadDataAsync异步方法
            RefreshCommand = new RelayCommand(async _ => await LoadDataAsync());
            // 上一页命令 - 绑定到PreviousPage方法，当前页大于1时可用
            PrevPageCommand = new RelayCommand(_ => PreviousPage(), _ => CurrentPage > 1);
            // 下一页命令 - 绑定到NextPage方法，当前页小于总页数时可用
            NextPageCommand = new RelayCommand(_ => NextPage(), _ => CurrentPage < TotalPages);
            // 编辑命令 - 绑定到EditItem方法
            EditCommand = new RelayCommand(item => EditItem(item as ProcessComposition));
            // 删除命令 - 绑定到DeleteItem异步方法
            DeleteCommand = new RelayCommand(async item => await DeleteItem(item as ProcessComposition));
            // 新增命令 - 绑定到AddItem方法
            AddCommand = new RelayCommand(_ => AddItem());

            // 加载初始数据（异步执行，不等待结果）
            _ = LoadDataAsync();
        }

        #region 属性区域

        /// <summary>
        /// 工艺路线组成数据集合属性
        /// 可观察集合，支持数据绑定和变更通知
        /// </summary>
        public ObservableCollection<ProcessComposition> ProcessCompositions
        {
            // 获取工艺路线组成数据集合
            get => _processCompositions;
            // 设置工艺路线组成数据集合，并触发属性变更通知
            set { _processCompositions = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 工艺路线ID筛选条件属性
        /// 用于根据工艺路线ID筛选数据
        /// </summary>
        public string ProcessRouteIdFilter
        {
            // 获取工艺路线ID筛选条件
            get => _processRouteIdFilter;
            // 设置工艺路线ID筛选条件，并触发属性变更通知
            set { _processRouteIdFilter = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 当前页码属性
        /// 用于分页显示数据
        /// </summary>
        public int CurrentPage
        {
            // 获取当前页码
            get => _currentPage;
            // 设置当前页码，并触发属性变更通知（包括当前页显示文本）
            set { _currentPage = value; OnPropertyChanged(); OnPropertyChanged(nameof(CurrentPageDisplay)); }
        }

        /// <summary>
        /// 每页显示数量属性
        /// 用于控制分页大小
        /// </summary>
        public int PageSize
        {
            // 获取每页显示数量
            get => _pageSize;
            // 设置每页显示数量，触发属性变更通知并重新加载数据
            set { _pageSize = value; OnPropertyChanged(); _ = LoadDataAsync(); }
        }

        /// <summary>
        /// 总记录数属性
        /// 显示数据的总数量
        /// </summary>
        public int TotalCount
        {
            // 获取总记录数
            get => _totalCount;
            // 设置总记录数，并触发属性变更通知
            set { _totalCount = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 总页数属性
        /// 根据总记录数和每页显示数量计算得出
        /// </summary>
        public int TotalPages
        {
            // 获取总页数
            get => _totalPages;
            // 设置总页数，并触发属性变更通知
            set { _totalPages = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 当前页显示文本属性（只读）
        /// 格式化显示当前页和总页数信息
        /// </summary>
        public string CurrentPageDisplay => $"第 {CurrentPage} 页 / 共 {TotalPages} 页";

        #endregion

        #region 命令区域

        /// <summary>
        /// 搜索命令 - 用于执行数据搜索操作
        /// </summary>
        public ICommand SearchCommand { get; }

        /// <summary>
        /// 清空命令 - 用于清空搜索条件
        /// </summary>
        public ICommand ClearCommand { get; }

        /// <summary>
        /// 刷新命令 - 用于刷新数据
        /// </summary>
        public ICommand RefreshCommand { get; }

        /// <summary>
        /// 上一页命令 - 用于翻到上一页
        /// </summary>
        public ICommand PrevPageCommand { get; }

        /// <summary>
        /// 下一页命令 - 用于翻到下一页
        /// </summary>
        public ICommand NextPageCommand { get; }

        /// <summary>
        /// 编辑命令 - 用于编辑选中的记录
        /// </summary>
        public ICommand EditCommand { get; }

        /// <summary>
        /// 删除命令 - 用于删除选中的记录
        /// </summary>
        public ICommand DeleteCommand { get; }

        /// <summary>
        /// 新增命令 - 用于打开新增工艺路线窗口
        /// </summary>
        public ICommand AddCommand { get; }

        #endregion

        #region 方法区域

        /// <summary>
        /// 异步加载数据的方法
        /// 从API获取工艺路线组成数据并更新UI
        /// </summary>
        private async Task LoadDataAsync()
        {
            try
            {
                // 设置HTTP请求的认证头
                // 检查认证令牌是否存在
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    // 清空现有的请求头
                    _httpClient.DefaultRequestHeaders.Clear();
                    // 添加Bearer认证头
                    _httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                // 构建API请求URL
                var url = $"http://localhost:5005/api/processRoute/processCompositionList";
                // 如果有工艺路线ID筛选条件，添加到URL参数中
                if (!string.IsNullOrWhiteSpace(ProcessRouteIdFilter))
                {
                    url += $"?processRouteId={ProcessRouteIdFilter}";
                }

                // 发送HTTP GET请求
                var response = await _httpClient.GetAsync(url);
                // 读取响应内容为字符串
                var jsonString = await response.Content.ReadAsStringAsync();

                // 调试输出：显示原始JSON响应内容
                System.Diagnostics.Debug.WriteLine($"API Response: {jsonString}");

                // 配置JSON反序列化选项
                var jsonOptions = new JsonSerializerOptions
                {
                    // 忽略属性名大小写
                    PropertyNameCaseInsensitive = true,
                    // 使用驼峰命名策略
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    // 允许从字符串读取数字
                    NumberHandling = JsonNumberHandling.AllowReadingFromString,
                    // 写入时忽略null值
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
                };

                // 首先尝试使用简化模型进行反序列化测试
                // 这是为了处理API返回数据类型不一致的问题
                try
                {
                    // 使用简化模型反序列化JSON数据
                    var simpleResult = JsonSerializer.Deserialize<ApiResult<List<ProcessCompositionSimple>>>(jsonString, jsonOptions);
                    // 检查反序列化结果是否有效
                    if (simpleResult?.Result != null)
                    {
                        // 将简化模型转换为完整的ProcessComposition模型
                        var convertedData = simpleResult.Result.Select(simple => new ProcessComposition
                        {
                            // 序列号直接赋值 - 移除@/ProcessRoute和@/Procedure前缀
                            SerialCode = simple.SerialCodeDisplay?.Replace("@/ProcessRoute", "").Replace("@/Procedure", ""),
                            // 工艺脚本ID，尝试解析为整数，失败则为0
                            ProcessScriptId = int.TryParse(simple.ProcessScriptIdDisplay, out int pid) ? pid : 0,
                            // 下一工序直接赋值
                            NextProcedure = simple.NextProcedureDisplay,
                            // 关系直接赋值
                            Relationship = simple.RelationshipDisplay,
                            // 是否关键工序，判断显示文本是否为"是"
                            IsKeyProcess = simple.IsKeyProcessDisplay == "是",
                            // 显示图标直接赋值
                            DisplayIcon = simple.DisplayIconDisplay,
                            // 准备时间直接赋值
                            PreparationTime = simple.PreparationTimeDisplay,
                            // 等待时间，尝试解析为整数，失败则为0
                            WaitingTime = int.TryParse(simple.WaitingTimeDisplay, out int wt) ? wt : 0,
                            // 备注直接赋值
                            Remarks = simple.RemarksDisplay,
                            // 工艺路线ID直接赋值
                            ProcessRouteId = simple.ProcessRouteIdDisplay,
                            // 是否删除，判断状态显示是否为"已删除"
                            IsDelete = simple.StatusDisplay == "已删除",
                            // 创建时间，尝试解析为日期时间，失败则为当前时间
                            CreateTime = DateTime.TryParse(simple.CreateTimeDisplay, out DateTime ct) ? ct : DateTime.Now,
                            // 创建者用户ID直接赋值
                            CreatorUserId = simple.CreatorUserIdDisplay,
                            // 创建者用户名直接赋值
                            CreatorUserName = simple.CreatorUserNameDisplay,
                            // 更新者用户名直接赋值
                            UpdateUserName = simple.UpdateUserNameDisplay,
                            // 记录ID直接赋值
                            Id = simple.IdDisplay
                        }).ToList();

                        // 获取转换后的所有数据
                        var allData = convertedData;
                        // 设置总记录数
                        TotalCount = allData.Count;
                        // 计算总页数（向上取整）
                        TotalPages = (int)Math.Ceiling((double)TotalCount / PageSize);

                        // 分页处理：跳过前面的页面，取当前页的数据
                        var pagedData = allData.Skip((CurrentPage - 1) * PageSize).Take(PageSize).ToList();
                        // 更新UI绑定的数据集合
                        ProcessCompositions = new ObservableCollection<ProcessComposition>(pagedData);
                        // 成功处理，直接返回
                        return;
                    }
                }
                catch (Exception simpleEx)
                {
                    // 简化模型反序列化失败，输出调试信息
                    System.Diagnostics.Debug.WriteLine($"Simple model failed: {simpleEx.Message}");
                }

                // 如果简化模型失败，尝试使用原始模型进行反序列化
                var apiResult = JsonSerializer.Deserialize<ApiResult<List<ProcessComposition>>>(jsonString, jsonOptions);

                // 检查原始模型反序列化结果是否有效
                if (apiResult?.Result != null)
                {
                    // 获取所有数据
                    var allData = apiResult.Result;
                    // 设置总记录数
                    TotalCount = allData.Count;
                    // 计算总页数（向上取整）
                    TotalPages = (int)Math.Ceiling((double)TotalCount / PageSize);

                    // 分页处理：跳过前面的页面，取当前页的数据
                    var pagedData = allData.Skip((CurrentPage - 1) * PageSize).Take(PageSize).ToList();
                    // 更新UI绑定的数据集合
                    ProcessCompositions = new ObservableCollection<ProcessComposition>(pagedData);
                }
                else
                {
                    // 如果没有数据，清空集合并重置计数
                    ProcessCompositions.Clear();
                    TotalCount = 0;
                    TotalPages = 0;
                }
            }
            // 捕获JSON解析异常
            catch (JsonException jsonEx)
            {
                // 显示JSON解析错误消息框
                MessageBox.Show($"JSON解析失败: {jsonEx.Message}\n\n请检查API返回的数据格式", "JSON错误", MessageBoxButton.OK, MessageBoxImage.Error);
                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"JSON Exception: {jsonEx}");
            }
            // 捕获HTTP请求异常
            catch (HttpRequestException httpEx)
            {
                // 显示网络错误消息框
                MessageBox.Show($"网络请求失败: {httpEx.Message}", "网络错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            // 捕获其他所有异常
            catch (Exception ex)
            {
                // 显示通用错误消息框
                MessageBox.Show($"加载数据失败: {ex.Message}\n\n详细信息: {ex}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"General Exception: {ex}");
            }
        }

        /// <summary>
        /// 清空筛选条件的方法
        /// 重置搜索条件并重新加载数据
        /// </summary>
        private void ClearFilter()
        {
            // 清空工艺路线ID筛选条件
            ProcessRouteIdFilter = string.Empty;
            // 重置到第一页
            CurrentPage = 1;
            // 重新加载数据（异步执行，不等待结果）
            _ = LoadDataAsync();
        }

        /// <summary>
        /// 上一页的方法
        /// 翻到上一页并重新加载数据
        /// </summary>
        private void PreviousPage()
        {
            // 检查当前页是否大于1
            if (CurrentPage > 1)
            {
                // 页码减1
                CurrentPage--;
                // 重新加载数据（异步执行，不等待结果）
                _ = LoadDataAsync();
            }
        }

        /// <summary>
        /// 下一页的方法
        /// 翻到下一页并重新加载数据
        /// </summary>
        private void NextPage()
        {
            // 检查当前页是否小于总页数
            if (CurrentPage < TotalPages)
            {
                // 页码加1
                CurrentPage++;
                // 重新加载数据（异步执行，不等待结果）
                _ = LoadDataAsync();
            }
        }

        /// <summary>
        /// 新增项目的方法
        /// 打开新增工艺路线窗口
        /// </summary>
        private void AddItem()
        {
            try
            {
                // 创建并显示新增工艺路线窗口
                var addWindow = new AddProcessRouteWindow();
                addWindow.ShowDialog();

                // 窗口关闭后刷新数据
                _ = LoadDataAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开新增窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 编辑项目的方法
        /// 处理编辑选中的工艺路线组成记录
        /// </summary>
        /// <param name="item">要编辑的工艺路线组成对象</param>
        private void EditItem(ProcessComposition item)
        {
            // 检查传入的项目是否为空
            if (item != null)
            {
                // 显示编辑功能待实现的提示消息
                MessageBox.Show($"编辑功能待实现\n选中项ID: {item.Id}", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 异步删除项目的方法
        /// 处理删除选中的工艺路线组成记录
        /// </summary>
        /// <param name="item">要删除的工艺路线组成对象</param>
        private async Task DeleteItem(ProcessComposition item)
        {
            // 检查传入的项目是否为空
            if (item != null)
            {
                // 显示确认删除的对话框
                var result = MessageBox.Show($"确定要删除这条记录吗？\nID: {item.Id}", "确认删除",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                // 如果用户确认删除
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // 这里应该调用删除API（目前显示待实现提示）
                        MessageBox.Show("删除功能待实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                        // await DeleteItemFromApi(item.Id); // 实际的删除API调用
                        // await LoadDataAsync(); // 删除成功后重新加载数据
                    }
                    catch (Exception ex)
                    {
                        // 捕获删除过程中的异常并显示错误消息
                        MessageBox.Show($"删除失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        #endregion

        #region INotifyPropertyChanged实现

        /// <summary>
        /// 属性变更事件 - 当属性值发生变化时触发
        /// 用于通知UI更新绑定的数据
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 触发属性变更通知的方法
        /// 使用CallerMemberName特性自动获取调用方法的属性名
        /// </summary>
        /// <param name="propertyName">发生变更的属性名，自动获取</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            // 如果有订阅者，触发属性变更事件
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}


