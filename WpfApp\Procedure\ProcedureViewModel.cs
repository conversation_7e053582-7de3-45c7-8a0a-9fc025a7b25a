using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Net.Http;
using System.Runtime.CompilerServices;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using WpfApp.Common;
using System.Linq;

namespace WpfApp.Procedure
{
    /// <summary>
    /// 工序管理视图模型类
    /// </summary>
    public class ProcedureViewModel : INotifyPropertyChanged
    {
        private readonly HttpClient _httpClient;
        private ObservableCollection<ProcessStep> _processSteps;
        private ProcessStep _selectedProcessStep;
        private int _currentPage = 1;
        private int _pageSize = 10;
        private int _totalCount = 0;
        private bool _isAllSelected = false;
        private bool _isUpdatingSelection = false;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public ProcedureViewModel()
        {
            // 初始化HTTP客户端
            _httpClient = new HttpClient();
            
            
            // 初始化工序集合
            ProcessSteps = new ObservableCollection<ProcessStep>();
            
            // 初始化命令
            InitializeCommands();
            
            // 加载数据
            LoadDataAsync();
        }

        /// <summary>
        /// 工序数据集合
        /// </summary>
        public ObservableCollection<ProcessStep> ProcessSteps
        {
            get => _processSteps;
            set
            {
                // 如果之前有数据，先取消事件订阅
                if (_processSteps != null)
                {
                    foreach (var item in _processSteps)
                    {
                        item.PropertyChanged -= OnProcessStepSelectionChanged;
                    }
                }

                _processSteps = value;

                // 为新数据订阅事件
                if (_processSteps != null)
                {
                    foreach (var item in _processSteps)
                    {
                        item.PropertyChanged += OnProcessStepSelectionChanged;
                    }
                }

                OnPropertyChanged();
                UpdateIsAllSelected();
            }
        }

        /// <summary>
        /// 当前选中的工序
        /// </summary>
        public ProcessStep SelectedProcessStep
        {
            get => _selectedProcessStep;
            set
            {
                _selectedProcessStep = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int CurrentPage
        {
            get => _currentPage;
            set
            {
                _currentPage = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CurrentPageDisplay));
            }
        }

        /// <summary>
        /// 每页显示数量
        /// </summary>
        public int PageSize
        {
            get => _pageSize;
            set
            {
                _pageSize = value;
                OnPropertyChanged();
                LoadDataAsync();
            }
        }

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount
        {
            get => _totalCount;
            set
            {
                _totalCount = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 当前页显示文本
        /// </summary>
        public string CurrentPageDisplay => $"第 {CurrentPage} 页";

        /// <summary>
        /// 是否全选
        /// </summary>
        public bool IsAllSelected
        {
            get => _isAllSelected;
            set
            {
                if (_isAllSelected != value)
                {
                    _isAllSelected = value;
                    OnPropertyChanged();

                    // 更新所有项目的选择状态
                    _isUpdatingSelection = true;
                    try
                    {
                        if (ProcessSteps != null)
                        {
                            foreach (var item in ProcessSteps)
                            {
                                item.IsSelected = value;
                            }
                        }
                    }
                    finally
                    {
                        _isUpdatingSelection = false;
                    }
                }
            }
        }

        // 命令属性
        public ICommand AddCommand { get; private set; }
        public ICommand EditCommand { get; private set; }
        public ICommand DeleteCommand { get; private set; }
        public ICommand DeleteSelectedCommand { get; private set; }
        public ICommand RefreshCommand { get; private set; }
        public ICommand ExportCommand { get; private set; }
        public ICommand PrevPageCommand { get; private set; }
        public ICommand NextPageCommand { get; private set; }

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            AddCommand = new WpfApp.Bom.RelayCommand(_ => ExecuteAdd());
            EditCommand = new WpfApp.Bom.RelayCommand(param => ExecuteEdit(param as ProcessStep));
            DeleteCommand = new WpfApp.Bom.RelayCommand(param => ExecuteDelete(param as ProcessStep));
            DeleteSelectedCommand = new WpfApp.Bom.RelayCommand(_ => ExecuteDeleteSelected());
            RefreshCommand = new WpfApp.Bom.RelayCommand(_ => LoadDataAsync());
            ExportCommand = new WpfApp.Bom.RelayCommand(_ => ExecuteExport());
            PrevPageCommand = new WpfApp.Bom.RelayCommand(_ => ExecutePrevPage(), _ => CurrentPage > 1);
            NextPageCommand = new WpfApp.Bom.RelayCommand(_ => ExecuteNextPage(), _ => ProcessSteps.Count >= PageSize);
        }

        /// <summary>
        /// 加载工序数据
        /// </summary>
        private async Task LoadDataAsync()
        {
            try
            {
                using var client = new HttpClient();
                
                var url = "http://localhost:5005/api/processRoute/processStepList";
                var response = await client.GetAsync(url);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };
                    
                    var apiResult = JsonSerializer.Deserialize<ApiResult<List<ProcessStep>>>(content, options);
                    
                    if (apiResult?.Code == 200 && apiResult.Result != null)
                    {
                        var allItems = apiResult.Result;
                        
                        ProcessSteps.Clear();
                        TotalCount = allItems.Count;
                        
                        // 设置行号
                        for (int i = 0; i < allItems.Count; i++)
                        {
                            allItems[i].RowNumber = i + 1;
                        }

                        var pageItems = allItems
                            .Skip((CurrentPage - 1) * PageSize)
                            .Take(PageSize)
                            .ToList();

                        foreach (var item in pageItems)
                        {
                            // 订阅选择状态变化事件
                            item.PropertyChanged += OnProcessStepSelectionChanged;
                            ProcessSteps.Add(item);
                        }

                        // 更新全选状态
                        UpdateIsAllSelected();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载工序数据时发生错误: {ex.Message}", "异常",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 处理工序选择状态变化
        /// </summary>
        private void OnProcessStepSelectionChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(ProcessStep.IsSelected) && !_isUpdatingSelection)
            {
                UpdateIsAllSelected();
            }
        }

        /// <summary>
        /// 更新全选状态
        /// </summary>
        private void UpdateIsAllSelected()
        {
            if (ProcessSteps == null || ProcessSteps.Count == 0)
            {
                _isAllSelected = false;
            }
            else
            {
                _isAllSelected = ProcessSteps.All(p => p.IsSelected);
            }
            OnPropertyChanged(nameof(IsAllSelected));
        }

        // 命令执行方法
        private void ExecuteAdd()
        {
            MessageBox.Show("新增工序功能开发中", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        private void ExecuteEdit(ProcessStep step)
        {
            if (step != null)
            {
                MessageBox.Show($"编辑工序: {step.ProcessName}，ID: {step.Id}", "提示", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        
        private void ExecuteDelete(ProcessStep step)
        {
            if (step != null)
            {
                var result = MessageBox.Show($"确定要删除工序 \"{step.ProcessName}\" 吗?", "确认删除", 
                    MessageBoxButton.YesNo, MessageBoxImage.Question);
                    
                if (result == MessageBoxResult.Yes)
                {
                    // 实际删除逻辑待实现
                    MessageBox.Show("删除功能开发中", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }
        
        private void ExecuteDeleteSelected()
        {
            var selectedItems = ProcessSteps.Where(p => p.IsSelected).ToList();
            if (selectedItems.Any())
            {
                var result = MessageBox.Show($"确定要删除选中的 {selectedItems.Count} 条工序记录吗?", "确认删除", 
                    MessageBoxButton.YesNo, MessageBoxImage.Question);
                    
                if (result == MessageBoxResult.Yes)
                {
                    // 实际删除逻辑待实现
                    MessageBox.Show("批量删除功能开发中", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            else
            {
                MessageBox.Show("请至少选择一条记录", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        
        private void ExecuteExport()
        {
            MessageBox.Show("导出功能开发中", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        private void ExecutePrevPage()
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
                LoadDataAsync();
            }
        }
        
        private void ExecuteNextPage()
        {
            CurrentPage++;
            LoadDataAsync();
        }

        /// <summary>
        /// 属性变更事件
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 触发属性变更通知
        /// </summary>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
} 
