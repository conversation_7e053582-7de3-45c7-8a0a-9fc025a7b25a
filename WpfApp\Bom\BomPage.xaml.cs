﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace WpfApp
{
    public partial class BomPage : UserControl
    {
        public BomPage()
        {
            InitializeComponent();
            this.DataContext = new BomViewModel();

            // 为DataGrid添加行号
            this.Loaded += BomPage_Loaded;
        }

        private void BomPage_Loaded(object sender, RoutedEventArgs e)
        {
            var dataGrid = FindName("bomDataGrid") as DataGrid;
            if (dataGrid != null)
            {
                dataGrid.LoadingRow += DataGrid_LoadingRow;
            }

            // 订阅ViewModel的属性变化事件来更新全选复选框
            if (DataContext is BomViewModel viewModel)
            {
                viewModel.PropertyChanged += ViewModel_PropertyChanged;
            }
        }

        private void ViewModel_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(BomViewModel.IsAllSelected))
            {
                var selectAllCheckBox = FindName("selectAllCheckBox") as CheckBox;
                if (selectAllCheckBox != null && DataContext is BomViewModel viewModel)
                {
                    selectAllCheckBox.IsChecked = viewModel.IsAllSelected;
                }
            }
        }

        private void DataGrid_LoadingRow(object sender, DataGridRowEventArgs e)
        {
            e.Row.Header = (e.Row.GetIndex() + 1).ToString();
        }

        private void SelectAllCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            if (DataContext is BomViewModel viewModel)
            {
                viewModel.IsAllSelected = true;
            }
        }

        private void SelectAllCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            if (DataContext is BomViewModel viewModel)
            {
                viewModel.IsAllSelected = false;
            }
        }
    }
}
