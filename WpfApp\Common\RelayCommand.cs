using System;
using System.Threading.Tasks;
using System.Windows.Input;

namespace WpfApp.Common
{
    /// <summary>
    /// 通用中继命令类 - 实现ICommand接口
    /// 支持同步和异步两种执行方式
    /// 用于在MVVM模式中绑定命令到UI控件
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Func<object, Task> _executeAsync;
        private readonly Action<object> _execute;
        private readonly Predicate<object> _canExecute;

        /// <summary>
        /// 同步命令的构造函数
        /// </summary>
        /// <param name="execute">要执行的同步操作</param>
        /// <param name="canExecute">判断命令是否可执行的条件，可选</param>
        public RelayCommand(Action<object> execute, Predicate<object> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        /// <summary>
        /// 异步命令的构造函数
        /// </summary>
        /// <param name="executeAsync">要执行的异步操作</param>
        /// <param name="canExecute">判断命令是否可执行的条件，可选</param>
        public RelayCommand(Func<object, Task> executeAsync, Predicate<object> canExecute = null)
        {
            _executeAsync = executeAsync ?? throw new ArgumentNullException(nameof(executeAsync));
            _canExecute = canExecute;
        }

        /// <summary>
        /// 判断命令是否可以执行的方法
        /// </summary>
        /// <param name="parameter">命令参数</param>
        /// <returns>如果命令可以执行返回true，否则返回false</returns>
        public bool CanExecute(object parameter)
        {
            return _canExecute?.Invoke(parameter) ?? true;
        }

        /// <summary>
        /// 执行命令的方法
        /// 根据命令类型（同步或异步）执行相应的操作
        /// </summary>
        /// <param name="parameter">命令参数</param>
        public async void Execute(object parameter)
        {
            if (_executeAsync != null)
                await _executeAsync(parameter);
            else
                _execute?.Invoke(parameter);
        }

        /// <summary>
        /// 命令可执行状态变更事件
        /// 当命令的可执行状态发生变化时触发
        /// </summary>
        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }
    }
}
