﻿<UserControl x:Class="WpfApp.WorkTask.ShowTaskPage"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WpfApp.WorkTask"
        mc:Ignorable="d"
        >
    <Grid Background="#ffffff">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!--搜索条件-->
        <Border Grid.Row="0" Background="#F8F9FA" Padding="10" Margin="0,0,0,10">
            <StackPanel>
                <TextBlock Text="工单任务" FontSize="18" FontWeight="Bold" Margin="0,0,0,15"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="任务编号:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox Grid.Column="1" Text="{Binding TaskCode}" Height="30" Margin="0,0,20,0"/>

                    <TextBlock Grid.Column="2" Text="工单编号:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox Grid.Column="3" Text="{Binding WorkOrderCode}" Height="30" Margin="0,0,20,0"/>

                    <TextBlock Grid.Column="4" Text="工序:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox Grid.Column="5" Text="{Binding ProcessStepId}" Height="30" Margin="0,0,20,0"/>

                    <TextBlock Grid.Column="6" Text="状态:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <ComboBox Grid.Column="7" SelectedValue="{Binding Status}" Height="30" Margin="0,0,20,0">
                        <ComboBox.Items>
                            <ComboBoxItem Content="全部" Tag="{x:Null}"/>
                            <ComboBoxItem Content="派工" Tag="1"/>
                            <ComboBoxItem Content="开工" Tag="2"/>
                            <ComboBoxItem Content="报工" Tag="3"/>
                        </ComboBox.Items>
                    </ComboBox>

                    <StackPanel Grid.Column="9" Orientation="Horizontal">
                        <Button Content="查询" Command="{Binding SearchCommand}" Width="80" Height="30" Margin="5,0"/>
                        <Button Content="重置" Command="{Binding ResetCommand}" Width="80" Height="30" Margin="5,0"/>
                    </StackPanel>
                </Grid>
            </StackPanel>
        </Border>
        <!--任务列表-->
        <DataGrid Grid.Row="1" ItemsSource="{Binding Tasks}" AutoGenerateColumns="False" 
                  CanUserAddRows="False" CanUserDeleteRows="False" 
                  GridLinesVisibility="All" HeadersVisibility="All"
                  AlternatingRowBackground="#F8F9FA" RowHeight="40">
            <DataGrid.Columns>
                <DataGridTextColumn Header="序号" Binding="{Binding Index}" Width="60" IsReadOnly="True"/>
                <DataGridTextColumn Header="任务编号" Binding="{Binding TaskNumber}" Width="120" IsReadOnly="True"/>
                <DataGridTextColumn Header="任务名称" Binding="{Binding TaskName}" Width="150" IsReadOnly="True"/>
                <DataGridTextColumn Header="站点名称" Binding="{Binding SiteName}" Width="100" IsReadOnly="True"/>
                <DataGridTextColumn Header="工单编号" Binding="{Binding WorkOrderCode}" Width="120" IsReadOnly="True"/>
                <DataGridTextColumn Header="工单名称" Binding="{Binding WorkOrderName}" Width="150" IsReadOnly="True"/>
                <DataGridTextColumn Header="工艺路线" Binding="{Binding ProcessRouteName}" Width="120" IsReadOnly="True"/>
                <DataGridTextColumn Header="工序名称" Binding="{Binding ProcessName}" Width="100" IsReadOnly="True"/>
                <DataGridTextColumn Header="任务颜色" Binding="{Binding TaskColor}" Width="80" IsReadOnly="True"/>
                <DataGridTextColumn Header="计划数量" Binding="{Binding PlanQuantity}" Width="80" IsReadOnly="True"/>
                <DataGridTextColumn Header="实际生产" Binding="{Binding RealityQuantity}" Width="80" IsReadOnly="True"/>
                <DataGridTextColumn Header="状态" Binding="{Binding StatusText}" Width="80" IsReadOnly="True"/>

                <DataGridTemplateColumn Header="操作" Width="150">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <Button Content="派工" Command="{Binding DataContext.DispatchCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}" 
                                        CommandParameter="{Binding}" Width="50" Height="25" Margin="2"/>
                                <Button Content="报工" Command="{Binding DataContext.StartCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}" 
                                        CommandParameter="{Binding}" Width="50" Height="25" Margin="2"/>
                                <Button Content="关闭" Command="{Binding DataContext.CloseCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}" 
                                        CommandParameter="{Binding}" Width="50" Height="25" Margin="2"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
        <!--分页控件-->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="10" Margin="0,10,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="共" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding Total}" Margin="0,0,5,0"/>
                    <TextBlock Text="条记录，第" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding Page}" Margin="0,0,5,0"/>
                    <TextBlock Text="/" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding TotalPages}" Margin="0,0,5,0"/>
                    <TextBlock Text="页" Margin="0,0,20,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="首页" Command="{Binding FirstPageCommand}" Width="60" Height="30" Margin="2"/>
                    <Button Content="上一页" Command="{Binding PrevPageCommand}" Width="60" Height="30" Margin="2"/>
                    <Button Content="下一页" Command="{Binding NextPageCommand}" Width="60" Height="30" Margin="2"/>
                    <Button Content="末页" Command="{Binding LastPageCommand}" Width="60" Height="30" Margin="2"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
