# BOM物料ID存储问题修复总结

## 问题描述
在BOM添加功能中，当用户选择物料配件时，选中的物料数据没有正确存储ID到对应的字段中，导致保存时物料ID列表为空。

## 问题原因分析

### 1. MaterialItem类缺少Id字段的绑定
- `MaterialItem`类虽然有`Id`属性，但在物料选择过程中没有正确设置该字段
- 在`AddMaterial()`方法中，从物料选择对话框获取的物料数据没有设置`Id`值

### 2. MaterialSelectItem类缺少Id字段
- `MaterialSelectItem`类没有`Id`属性，导致物料选择时无法传递ID信息
- API响应数据模型`MaterialApiItem`和`ProductApiItem`也缺少`Id`字段

### 3. UpdateMaterialIDs方法依赖Id字段
- `UpdateMaterialIDs()`方法依赖于`MaterialItem.Id`字段来生成物料ID列表
- 由于Id字段没有被正确设置，导致生成的MaterialIDs列表为空

## 修复内容

### 1. 修复MaterialSelectItem类
**文件**: `WpfApp/Bom/MaterialSelectViewModel.cs`
- 添加了`Id`属性：`public long Id { get; set; }`
- 确保物料选择时能正确传递ID信息

### 2. 修复API响应数据模型
**文件**: `WpfApp/Bom/MaterialSelectViewModel.cs`
- 在`MaterialApiItem`类中添加：`public long Id { get; set; }`
- 在`ProductApiItem`类中添加：`public long Id { get; set; }`
- 确保从API获取的数据包含ID信息

### 3. 修复物料选择数据处理
**文件**: `WpfApp/Bom/MaterialSelectViewModel.cs`
- 在`LoadDataAsync()`方法中，为物料和产品数据设置Id字段
- 在`LoadTestData()`和`LoadFallbackData()`方法中，为测试数据添加Id字段

### 4. 修复AddMaterial方法
**文件**: `WpfApp/Bom/BomEditViewModel.cs`
- 在`AddMaterial()`方法中添加：`materialItem.Id = selectedMaterial.Id;`
- 确保从物料选择对话框获取的物料ID正确设置到MaterialItem中

### 5. 增强UpdateMaterialIDs方法
**文件**: `WpfApp/Bom/BomEditViewModel.cs`
- 添加详细的调试日志，便于跟踪物料ID的更新过程
- 优化代码逻辑，确保正确生成MaterialIDs列表

### 6. 增强Save方法
**文件**: `WpfApp/Bom/BomEditViewModel.cs`
- 在保存前添加调试日志，显示MaterialIDs的内容
- 确保在调用API前物料ID列表已正确更新

## 修复效果

### 修复前
- 物料选择后，MaterialItem的Id字段为0
- UpdateMaterialIDs方法生成的MaterialIDs列表为空
- 保存时物料ID信息丢失

### 修复后
- 物料选择时正确设置MaterialItem的Id字段
- UpdateMaterialIDs方法能正确生成包含选中物料ID的列表
- 保存时物料ID信息完整传递到API

## 测试建议

1. **物料选择测试**
   - 打开BOM添加页面
   - 点击"添加"按钮选择物料
   - 验证选中的物料是否正确显示在列表中

2. **物料ID存储测试**
   - 选择多个物料
   - 查看调试日志，确认MaterialItem的Id字段是否正确设置
   - 验证UpdateMaterialIDs方法生成的ID列表

3. **保存功能测试**
   - 填写必要的BOM信息
   - 选择物料后点击保存
   - 验证API请求中是否包含正确的物料ID数组

## 注意事项

1. **API兼容性**
   - 确保后端API能正确处理物料ID数组
   - 验证API响应格式是否包含物料ID字段

2. **数据类型一致性**
   - 使用`long`类型存储物料ID，确保与数据库字段类型一致
   - 在转换为int数组时注意数据范围

3. **错误处理**
   - 添加适当的异常处理，防止ID转换失败
   - 提供用户友好的错误提示

## 相关文件清单

- `WpfApp/Bom/BomEditViewModel.cs` - 主要修复文件
- `WpfApp/Bom/MaterialSelectViewModel.cs` - 物料选择相关修复
- `WpfApp/Bom/MaterialSelectDialog.xaml.cs` - 物料选择对话框
- `WpfApp/Bom/BomEditPage.xaml` - BOM编辑页面界面
- `WpfApp/Bom/BomEditPage.xaml.cs` - BOM编辑页面逻辑 