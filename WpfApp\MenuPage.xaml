﻿<UserControl x:Class="WpfApp.MenuPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:WpfApp"
             >
    <Grid>
        <Grid.Resources>
            <!-- 菜单按钮样式 -->
            <Style x:Key="MenuButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="Height" Value="45"/>
                <Setter Property="Margin" Value="0,2,0,2"/>
                <Setter Property="HorizontalContentAlignment" Value="Left"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border x:Name="border" Background="{TemplateBinding Background}" BorderThickness="3,0,0,0"
                                    BorderBrush="Transparent">
                                <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                  VerticalAlignment="Center" Margin="15,0,0,0"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="border" Property="Background" Value="#2C52B8"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="border" Property="Background" Value="#1A365D"/>
                                    <Setter TargetName="border" Property="BorderBrush" Value="#FFD700"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </Grid.Resources>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="180"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        <!-- 左侧菜单 -->
        <StackPanel Grid.Column="0" Background="#1E40AF" VerticalAlignment="Stretch">
            <!-- 菜单标题 -->
            <StackPanel Background="#1A365D" Height="60" VerticalAlignment="Top">
                <TextBlock Text="MES系统" FontSize="16" FontWeight="Bold" Foreground="White" Margin="15,5,0,0"/>
                <TextBlock Text="制造执行系统" FontSize="12" Foreground="#CCCCCC" Margin="15,0,0,0"/>
            </StackPanel>
            <!-- 菜单内容 -->
            <Button Command="{Binding ShowHomeCommand}" Style="{StaticResource MenuButtonStyle}">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M10,20v-6h4v6h5v-8h3L12,3 2,12h3v8z" Fill="White" Width="20" Height="20"/>
                    <TextBlock Text="首页" Margin="15,0,0,0" FontSize="14"/>
                </StackPanel>
            </Button>
            <Button Command="{Binding ShowProductionPlanCommand}" Style="{StaticResource MenuButtonStyle}">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M19,13h-6v6h-2v-6H5v-2h6V5h2v6h6V13z M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2z" Fill="White" Width="20" Height="20"/>
                    <TextBlock Text="生产计划" Margin="15,0,0,0" FontSize="14"/>
                </StackPanel>
            </Button>
            <Button Command="{Binding ShowWorkOrderCommand}" Style="{StaticResource MenuButtonStyle}">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M19,19H5V5H19M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M13.96,12.29L11.21,15.83L9.25,13.47L6.5,17H17.5L13.96,12.29Z" Fill="White" Width="20" Height="20"/>
                    <TextBlock Text="生产工单" Margin="15,0,0,0" FontSize="14"/>
                </StackPanel>
            </Button>
            <Button Command="{Binding ShowTaskCommand}" Style="{StaticResource MenuButtonStyle}">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19M7,7H17V9H7V7M7,11H17V13H7V11M7,15H14V17H7V15Z" Fill="White" Width="20" Height="20"/>
                    <TextBlock Text="工单任务" Margin="15,0,0,0" FontSize="14"/>
                </StackPanel>
            </Button>
            <Button Command="{Binding ShowReportCommand}" Style="{StaticResource MenuButtonStyle}">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19M7,7H17V9H7V7M7,11H17V13H7V11M7,15H14V17H7V15Z" Fill="White" Width="20" Height="20"/>
                    <TextBlock Text="报工记录" Margin="15,0,0,0" FontSize="14"/>
                </StackPanel>
            </Button>
            <Button Command="{Binding ShowQualityCommand}" Style="{StaticResource MenuButtonStyle}">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M18.5,1.15C17.97,1.15 17.46,1.34 17.07,1.73L11.26,7.55L16.91,13.2L22.73,7.39C23.5,6.61 23.5,5.35 22.73,4.56L20.35,2.18C19.96,1.79 19.45,1.59 18.93,1.59C18.7,1.59 18.47,1.64 18.24,1.74L18.5,1.15M10.3,8.5L3,15.8V18H5.2L12.5,10.7L10.3,8.5Z" Fill="White" Width="20" Height="20"/>
                    <TextBlock Text="质量检验" Margin="15,0,0,0" FontSize="14"/>
                </StackPanel>
            </Button>
            <Button Command="{Binding ShowProcessRouteCommand}" Style="{StaticResource MenuButtonStyle}">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M3,5H9V11H3V5M5,7V9H7V7H5M11,7H21V9H11V7M11,15H21V17H11V15M5,13V15H7V13H5M3,13H9V19H3V13Z" Fill="White" Width="20" Height="20"/>
                    <TextBlock Text="工艺路线" Margin="15,0,0,0" FontSize="14"/>
                </StackPanel>
            </Button>
            <Button Command="{Binding ShowProcedureCommand}" Style="{StaticResource MenuButtonStyle}">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M14,2H6C4.89,2 4,2.89 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M12,18L8,16V18H12M12,14L8,12V14H12M18,20H6V4H13V9H18V20M10,9V3.5L15.5,9H10Z" Fill="White" Width="20" Height="20"/>
                    <TextBlock Text="工序管理" Margin="15,0,0,0" FontSize="14"/>
                </StackPanel>
            </Button>
            <!-- 添加Bom管理菜单项 -->
            <Button Command="{Binding ShowBomCommand}" Style="{StaticResource MenuButtonStyle}">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M14,2H6C4.89,2 4,2.89 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M12,18L8,16V18H12M12,14L8,12V14H12M18,20H6V4H13V9H18V20M10,9V3.5L15.5,9H10Z" Fill="White" Width="20" Height="20"/>
                    <TextBlock Text="Bom管理" Margin="15,0,0,0" FontSize="14"/>
                </StackPanel>
            </Button>
            <!-- 测试添加工序功能 --><!--
            <Button Command="{Binding TestAddProcessStepCommand}" Style="{StaticResource MenuButtonStyle}">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" Fill="White" Width="20" Height="20"/>
                    <TextBlock Text="测试添加工序" Margin="15,0,0,0" FontSize="14"/>
                </StackPanel>
            </Button>
            --><!-- 新增工艺路线窗口 --><!--
            <Button Command="{Binding OpenAddProcessRouteWindowCommand}" Style="{StaticResource MenuButtonStyle}">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,7V13H13V7H11M11,15V17H13V15H11Z" Fill="White" Width="20" Height="20"/>
                    <TextBlock Text="新增工艺路线" Margin="15,0,0,0" FontSize="14"/>
                </StackPanel>
            </Button>-->
            <Button Command="{Binding ShowSettingsCommand}" Style="{StaticResource MenuButtonStyle}">
                <StackPanel Orientation="Horizontal">
                    <Path Data="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.21,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.21,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.67 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z" Fill="White" Width="20" Height="20"/>
                    <TextBlock Text="系统设置" Margin="15,0,0,0" FontSize="14"/>
                </StackPanel>
            </Button>
        </StackPanel>
        <!-- 右侧内容区 -->
        <!-- 右侧内容区 -->
        <ContentControl Grid.Column="1" Content="{Binding CurrentView}" x:Name="MenuContent" />
    </Grid>
</UserControl>