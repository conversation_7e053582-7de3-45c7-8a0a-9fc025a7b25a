﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Net.Http;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace WpfApp.WorkTask
{
    public class DispatchTaskViewModel : INotifyPropertyChanged
    {
        private string _taskId;
        private string _taskRemarks = "";
        private string _qualityRemarks = "";
        private string _taskResponsible = "";
        private string _otherMembers = "";
        private string _qualityDept = "";
        private string _qualityPerson = "";

        public string TaskId
        {
            get => _taskId;
            set { _taskId = value; OnPropertyChanged(); }
        }

        public string TaskRemarks
        {
            get => _taskRemarks;
            set { _taskRemarks = value; OnPropertyChanged(); }
        }

        public string QualityRemarks
        {
            get => _qualityRemarks;
            set { _qualityRemarks = value; OnPropertyChanged(); }
        }

        public string TaskResponsible
        {
            get => _taskResponsible;
            set { _taskResponsible = value; OnPropertyChanged(); }
        }

        public string OtherMembers
        {
            get => _otherMembers;
            set { _otherMembers = value; OnPropertyChanged(); }
        }

        public string QualityDept
        {
            get => _qualityDept;
            set { _qualityDept = value; OnPropertyChanged(); }
        }

        public string QualityPerson
        {
            get => _qualityPerson;
            set { _qualityPerson = value; OnPropertyChanged(); }
        }

        // 班组选项集合
        public ObservableCollection<TeamOption> TeamOptions { get; } = new ObservableCollection<TeamOption>();

        // 选中的班组
        public TeamOption SelectedTeam { get; set; }

        public ICommand CancelCommand { get; }
        public ICommand ConfirmCommand { get; }

        public DispatchTaskViewModel(string taskId)
        {
            TaskId = taskId;
            CancelCommand = new RelayCommand(_ => DialogClosed?.Invoke());
            ConfirmCommand = new RelayCommand(async _ => await ConfirmDispatchAsync());

            LoadTeamsAsync();
        }

        private async Task LoadTeamsAsync()
        {
            try
            {
                using var client = new HttpClient();
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                var response = await client.GetAsync("http://localhost:5005/api/task/classGroup");

                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };

                    var result = JsonSerializer.Deserialize<TeamApiResponse>(json, options);

                    if (result?.Code == 200 && result.Result != null)
                    {
                        TeamOptions.Clear();
                        foreach (var team in result.Result)
                        {
                            TeamOptions.Add(new TeamOption
                            {
                                Id = team.Id,
                                Name = team.ClassGroupName
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载班组数据失败: {ex.Message}");
            }
        }

        private async Task ConfirmDispatchAsync()
        {
            try
            {
                if (SelectedTeam == null || string.IsNullOrWhiteSpace(TaskResponsible) ||
                    string.IsNullOrWhiteSpace(QualityDept) || string.IsNullOrWhiteSpace(QualityPerson))
                {
                    MessageBox.Show("请填写所有必填项", "提示");
                    return;
                }

                using var client = new HttpClient();
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                var dispatchData = new
                {
                    TaskId = TaskId,
                    ClassGroupId = SelectedTeam.Id,
                    TaskResponsible = TaskResponsible,
                    Member = OtherMembers ?? "",
                    TaskRemark = TaskRemarks ?? "",
                    QualityTaskDepart = QualityDept,
                    QualityTaskResponsible = QualityPerson,
                    QualityTaskRemark = QualityRemarks ?? ""
                };

                var options = new JsonSerializerOptions
                {
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(dispatchData, options);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                System.Diagnostics.Debug.WriteLine($"派工请求URL: http://localhost:5005/api/task/dispatchWork");
                System.Diagnostics.Debug.WriteLine($"派工请求数据: {json}");

                var url = $"http://localhost:5005/api/task/dispatchWork?taskId={TaskId}&ClassGroupId={SelectedTeam.Id}&TaskResponsible={TaskResponsible}&Member={OtherMembers}&TaskRemark={TaskRemarks}&QualityTaskDepart={QualityDept}&QualityTaskResponsible={QualityPerson}&QualityTaskRemark={QualityRemarks}";
                var response = await client.PutAsync(url, content);

                var responseContent = await response.Content.ReadAsStringAsync();
                System.Diagnostics.Debug.WriteLine($"派工响应状态: {response.StatusCode}");
                System.Diagnostics.Debug.WriteLine($"派工响应内容: {responseContent}");

                if (response.IsSuccessStatusCode)
                {
                    MessageBox.Show("派工成功！", "成功");
                    DialogClosed?.Invoke();
                }
                else
                {
                    MessageBox.Show($"派工失败：{responseContent}", "错误");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"派工异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                MessageBox.Show($"派工时发生错误：{ex.Message}", "错误");
            }
        }

        public event Action DialogClosed;
        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
    public class TeamApiResponse
    {
        public int Code { get; set; }
        public string Type { get; set; }
        public string Message { get; set; }
        public List<TeamApiModel> Result { get; set; }
    }

    public class TeamApiModel
    {
        public string Id { get; set; }
        public string ClassGroupName { get; set; }
        public string ClassGroupCode { get; set; }
        public string ClassGroupType { get; set; }
        public string Leader { get; set; }
        public int MemberCount { get; set; }
    }
    public class TeamOption
    {
        public string Id { get; set; }
        public string Name { get; set; }
    }
}
