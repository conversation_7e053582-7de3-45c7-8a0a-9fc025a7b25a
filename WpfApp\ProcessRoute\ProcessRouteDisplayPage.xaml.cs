using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;

namespace WpfApp.ProcessRoute
{
    /// <summary>
    /// 工艺路线显示页面的交互逻辑类
    /// </summary>
    public partial class ProcessRouteDisplayPage : UserControl
    {
        private ProcessRouteDisplayViewModel _viewModel;

        /// <summary>
        /// 构造函数 - 初始化工艺路线显示页面
        /// </summary>
        public ProcessRouteDisplayPage()
        {
            // 初始化XAML组件
            InitializeComponent();
            
            // 设置数据上下文为视图模型实例
            _viewModel = new ProcessRouteDisplayViewModel();
            DataContext = _viewModel;
            
            // 订阅ViewModel属性变化事件
            _viewModel.PropertyChanged += ViewModel_PropertyChanged;
        }

        /// <summary>
        /// ViewModel属性变化事件处理
        /// </summary>
        private void ViewModel_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(ProcessRouteDisplayViewModel.IsAllSelected))
            {
                var selectAllCheckBox = FindName("selectAllCheckBox") as CheckBox;
                if (selectAllCheckBox != null && DataContext is ProcessRouteDisplayViewModel viewModel)
                {
                    selectAllCheckBox.IsChecked = viewModel.IsAllSelected;
                }
            }
        }

        /// <summary>
        /// 全选复选框选中事件
        /// </summary>
        private void SelectAllCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            if (DataContext is ProcessRouteDisplayViewModel viewModel)
            {
                viewModel.IsAllSelected = true;
            }
        }

        /// <summary>
        /// 全选复选框取消选中事件
        /// </summary>
        private void SelectAllCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            if (DataContext is ProcessRouteDisplayViewModel viewModel)
            {
                viewModel.IsAllSelected = false;
            }
        }
    }
}
