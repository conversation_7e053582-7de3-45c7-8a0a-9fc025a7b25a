using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Collections.Generic; // Added for List<BomProduct>
using System.Linq;
using System.Windows;
using System;
using WpfApp; // 引用 AuthContext
using WpfApp.Bom;

public class BomViewModel : INotifyPropertyChanged
{
    private int _page = 1;
    private int _pageSize = 11;
    private int _total;
    private ObservableCollection<BomProduct> _bomProducts = new();
    private bool _isAllSelected;
    private bool _isUpdatingSelection;

    // 导航事件
    public event Action<BomEditViewModel> NavigateToEdit;

    public int Page
    {
        get => _page;
        set { if (_page != value) { _page = value; OnPropertyChanged(nameof(Page)); LoadData(); } }
    }

    public int PageSize
    {
        get => _pageSize;
        set { if (_pageSize != value) { _pageSize = value; OnPropertyChanged(nameof(PageSize)); LoadData(); } }
    }

    public int Total
    {
        get => _total;
        set { _total = value; OnPropertyChanged(nameof(Total)); }
    }

    public ObservableCollection<BomProduct> BomProducts
    {
        get => _bomProducts;
        set { _bomProducts = value; OnPropertyChanged(nameof(BomProducts)); }
    }

    public ICommand PrevPageCommand { get; }
    public ICommand NextPageCommand { get; }
    public ICommand AddCommand { get; }
    public ICommand EditCommand { get; }
    public ICommand DeleteCommand { get; }
    public ICommand DeleteSelectedCommand { get; }

    public BomViewModel()
    {
        PrevPageCommand = new WpfApp.Bom.RelayCommand(_ => { if (Page > 1) Page--; });
        NextPageCommand = new WpfApp.Bom.RelayCommand(_ => { if (Page * PageSize < Total) Page++; });
        AddCommand = new WpfApp.Bom.RelayCommand(_ => AddBom());
        EditCommand = new WpfApp.Bom.RelayCommand(EditBom);
        DeleteCommand = new WpfApp.Bom.RelayCommand(DeleteBom);
        DeleteSelectedCommand = new WpfApp.Bom.RelayCommand(_ => DeleteSelected());
        LoadData();
    }

    private async void LoadData()
    {
        using var client = new HttpClient();
        if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
        {
            client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
        }
        var url = $"{ApiConfig.GetUrl(ApiConfig.Endpoints.PageBomEntity)}?Page={Page}&PageSize={PageSize}";
        var json = await client.GetStringAsync(url);
        var result = System.Text.Json.JsonSerializer.Deserialize<BomPageResponse>(json, new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true });
        var products = result?.Result?.Items ?? new List<BomProduct>();

        // 为每个产品订阅选择状态变化事件
        foreach (var product in products)
        {
            product.PropertyChanged += OnProductSelectionChanged;
        }

        BomProducts = new ObservableCollection<BomProduct>(products);
        Total = result?.Result?.Total ?? 0;

        // 更新全选状态
        UpdateIsAllSelected();
    }

    private void OnProductSelectionChanged(object sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(BomProduct.IsSelected) && !_isUpdatingSelection)
        {
            UpdateIsAllSelected();
        }
    }

    private void UpdateIsAllSelected()
    {
        if (BomProducts.Count == 0)
        {
            _isAllSelected = false;
        }
        else
        {
            _isAllSelected = BomProducts.All(p => p.IsSelected);
        }
        OnPropertyChanged(nameof(IsAllSelected));
    }

    public event PropertyChangedEventHandler PropertyChanged;
    protected void OnPropertyChanged(string name) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    public bool IsAllSelected
    {
        get => _isAllSelected;
        set
        {
            if (_isAllSelected != value)
            {
                _isAllSelected = value;
                OnPropertyChanged(nameof(IsAllSelected));
                
                // 更新所有项目的选择状态
                _isUpdatingSelection = true;
                try
                {
                    foreach (var item in BomProducts)
                    {
                        item.IsSelected = value;
                    }
                }
                finally
                {
                    _isUpdatingSelection = false;
                }
            }
        }
    }

    private void AddBom()
    {
        var viewModel = new BomEditViewModel();

        // 使用页面导航而不是对话框
        NavigateToEdit?.Invoke(viewModel);
    }

    // 添加保存新产品的方法，供页面导航使用
    public void AddNewProduct(BomProduct newProduct)
    {
        // 这里应该调用API保存到服务器
        // 暂时添加到本地列表
        newProduct.PropertyChanged += OnProductSelectionChanged;
        BomProducts.Add(newProduct);
        Total++;
    }

    private void EditBom(object parameter)
    {
        if (parameter is BomProduct product)
        {
            var viewModel = new BomEditViewModel(product);

            // 使用页面导航而不是对话框
            NavigateToEdit?.Invoke(viewModel);
        }
    }

    // 添加更新产品的方法，供页面导航使用
    public void UpdateProduct(BomProduct originalProduct, BomProduct updatedProduct)
    {
        // 这里应该调用API更新到服务器
        // 暂时更新本地对象
        var index = BomProducts.IndexOf(originalProduct);
        if (index >= 0)
        {
            var currentProduct = BomProducts[index];
            currentProduct.BomCode = updatedProduct.BomCode;
            currentProduct.BomVersion = updatedProduct.BomVersion;
            currentProduct.ProductId = updatedProduct.ProductId;
            currentProduct.ProductCode = updatedProduct.ProductCode;
            currentProduct.Specification = updatedProduct.Specification;
            currentProduct.Unit = updatedProduct.Unit;
            currentProduct.DailyOutput = updatedProduct.DailyOutput;
            currentProduct.IsSystemCode = updatedProduct.IsSystemCode;
            currentProduct.IsDefaultBom = updatedProduct.IsDefaultBom;
            currentProduct.Remarks = updatedProduct.Remarks;
            currentProduct.ProcessRouteId = updatedProduct.ProcessRouteId;
        }
    }

    private void DeleteBom(object parameter)
    {
        if (parameter is BomProduct product)
        {
            var result = MessageBox.Show($"确定要删除BOM编号为 {product.BomCode} 的记录吗？",
                                       "确认删除",
                                       MessageBoxButton.YesNo,
                                       MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // 这里应该调用API从服务器删除
                // 暂时从本地列表删除
                product.PropertyChanged -= OnProductSelectionChanged;
                BomProducts.Remove(product);
                Total--;
                UpdateIsAllSelected();
            }
        }
    }

    private void DeleteSelected()
    {
        var selectedProducts = BomProducts.Where(p => p.IsSelected).ToList();

        if (selectedProducts.Count == 0)
        {
            MessageBox.Show("请先选择要删除的记录。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        var result = MessageBox.Show($"确定要删除选中的 {selectedProducts.Count} 条记录吗？",
                                   "确认删除",
                                   MessageBoxButton.YesNo,
                                   MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            // 这里应该调用API批量删除
            // 暂时从本地列表删除
            foreach (var product in selectedProducts)
            {
                product.PropertyChanged -= OnProductSelectionChanged;
                BomProducts.Remove(product);
                Total--;
            }
            UpdateIsAllSelected();
        }
    }
}

public class BomPageResponse
{
    public int Code { get; set; }
    public string Type { get; set; }
    public string Message { get; set; }
    public BomPageResult Result { get; set; }
}
public class BomPageResult
{
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int Total { get; set; }
    public int TotalPages { get; set; }
    public List<BomProduct> Items { get; set; }
    public bool HasPrevPage { get; set; }
    public bool HasNextPage { get; set; }
}