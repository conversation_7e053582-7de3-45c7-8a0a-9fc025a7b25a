﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace WpfApp.WorkOrderDetail
{
    public class WorkOrderDetailModel
    {
        [JsonPropertyName("workOrderCode")]
        public string? WorkOrderCode { get; set; }

        [JsonPropertyName("workOrderName")]
        public string? WorkOrderName { get; set; }

        [JsonPropertyName("planId")]
        public string? PlanId { get; set; }

        [JsonPropertyName("planName")]
        public string? PlanName { get; set; }

        [JsonPropertyName("planCode")]
        public string? PlanCode { get; set; }

        [JsonPropertyName("sourceId")]
        public string? SourceId { get; set; }

        [JsonPropertyName("sourceName")]
        public string? SourceName { get; set; }

        [JsonPropertyName("productCode")]
        public string? ProductCode { get; set; }

        [JsonPropertyName("productName")]
        public string? ProductName { get; set; }

        [JsonPropertyName("specification")]
        public string? Specification { get; set; }

        [JsonPropertyName("unit")]
        public string? Unit { get; set; }

        [JsonPropertyName("productType")]
        public string? ProductType { get; set; }

        [JsonPropertyName("bomId")]
        public string? BomId { get; set; }

        [JsonPropertyName("bomCode")]
        public string? BomCode { get; set; }

        [JsonPropertyName("bomVersion")]
        public string? BomVersion { get; set; }

        [JsonPropertyName("planStartTime")]
        public string? PlanStartTime { get; set; }

        [JsonPropertyName("planEndTime")]
        public string? PlanEndTime { get; set; }

        [JsonPropertyName("demandTime")]
        public string? DemandTime { get; set; }

        [JsonPropertyName("planNumber")]
        public int? PlanNumber { get; set; }

        [JsonPropertyName("remarks")]
        public string? Remarks { get; set; }

        [JsonPropertyName("materialIds")]
        public int[] MaterialIds { get; set; }

        [JsonPropertyName("processRouteId")]
        public string? ProcessRouteId { get; set; }

        [JsonPropertyName("processRouteCode")]
        public string? ProcessRouteCode { get; set; }

        [JsonPropertyName("processRouteName")]
        public string? ProcessRouteName { get; set; }

        [JsonPropertyName("processSteps")]
        public string? ProcessSteps { get; set; }

        [JsonPropertyName("extras")]
        public object? Extras { get; set; }

        [JsonPropertyName("time")]
        public string? Time { get; set; }

        // 添加缺失的字段
        public string? OrderNumber { get; set; }
        public int? RealityNumber { get; set; }
        public string? RealityStartTime { get; set; }
        public string? RealityEndTime { get; set; }
        public int Status { get; set; }
        public string? CreateTime { get; set; }
        public string? CreateUser { get; set; }
    }

    public class WorkOrderDetailResponse
    {
        [JsonPropertyName("code")]
        public  int Code { get; set; }

        [JsonPropertyName("type")]
        public string? Type { get; set; }

        [JsonPropertyName("message")]
        public string? Message { get; set; }

        [JsonPropertyName("result")]
        public WorkOrderDetailModel Result { get; set; }
    }
    public class MaterialListResponse
    {
        [JsonPropertyName("code")]
        public int Code { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; }

        [JsonPropertyName("result")]
        public MaterialListResult Result { get; set; }
    }

    public class MaterialListResult
    {
        [JsonPropertyName("page")]
        public int Page { get; set; }

        [JsonPropertyName("pageSize")]
        public int PageSize { get; set; }

        [JsonPropertyName("total")]
        public int Total { get; set; }

        [JsonPropertyName("totalPages")]
        public int TotalPages { get; set; }

        [JsonPropertyName("items")]
        public List<MaterialItem> Items { get; set; }
    }

    public class MaterialItem
    {
        [JsonPropertyName("materialCode")]
        public string MaterialCode { get; set; }

        [JsonPropertyName("materialName")]
        public string MaterialName { get; set; }

        [JsonPropertyName("specification")]
        public string Specification { get; set; }

        [JsonPropertyName("unit")]
        public string Unit { get; set; }

        [JsonPropertyName("usageQuantity")]
        public decimal UsageQuantity { get; set; }

        [JsonPropertyName("usageRatio")]
        public decimal UsageRatio { get; set; }
    }

    public class ProcessStepResponse
    {
        [JsonPropertyName("code")]
        public int Code { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("result")]
        public List<ProcessStepItem> Result { get; set; }
    }

    public class ProcessStepItem
    {
        [JsonPropertyName("taskNumber")]
        public string TaskNumber { get; set; }

        [JsonPropertyName("taskName")]
        public string TaskName { get; set; }

        [JsonPropertyName("sitesId")]
        public string SitesId { get; set; }

        [JsonPropertyName("siteCode")]
        public string SiteCode { get; set; }

        [JsonPropertyName("siteName")]
        public string SiteName { get; set; }

        [JsonPropertyName("processStepId")]
        public string ProcessStepId { get; set; }

        [JsonPropertyName("processCode")]
        public string ProcessCode { get; set; }

        [JsonPropertyName("processRouteId")]
        public string ProcessRouteId { get; set; }

        [JsonPropertyName("processRouteCode")]
        public string ProcessRouteCode { get; set; }

        [JsonPropertyName("processRouteName")]
        public string ProcessRouteName { get; set; }

        [JsonPropertyName("taskColor")]
        public string TaskColor { get; set; }

        [JsonPropertyName("planQuantity")]
        public int? PlanQuantity { get; set; }

        [JsonPropertyName("startTime")]
        public string StartTime { get; set; }

        [JsonPropertyName("endTime")]
        public string EndTime { get; set; }

        [JsonPropertyName("taskStatus")]
        public int? TaskStatus { get; set; }

        [JsonPropertyName("createTime")]
        public string CreateTime { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }
    }

    // 添加ProcessStepModel用于UI绑定
    public class ProcessStepModel
    {
        public int Index { get; set; }
        public string TaskId { get; set; }
        public string TaskCode { get; set; }
        public string TaskName { get; set; }
        public string SiteName { get; set; }
        public string SiteCode { get; set; }
        public int PlanQuantity { get; set; }
        public string PlanStartTime { get; set; }
        public string PlanEndTime { get; set; }
        public string TaskColor { get; set; }
    }

    // 添加MaterialModel用于UI绑定
    public class MaterialModel
    {
        public int Index { get; set; }
        public string MaterialCode { get; set; }
        public string MaterialName { get; set; }
        public string Specification { get; set; }
        public string Unit { get; set; }
        public decimal RequiredQuantity { get; set; }
        public decimal UsageRatio { get; set; }
    }
}
