// 引用系统基础类库
using System;
// 引用JSON序列化注解类库
using System.Text.Json.Serialization;
// 引用组件模型类库
using System.ComponentModel;

// 定义工艺路线命名空间
namespace WpfApp.ProcessRoute
{
    /// <summary>
    /// 工序数据模型类
    /// 用于存储和传输工序的相关信息
    /// </summary>
    public class ProcessStep : INotifyPropertyChanged
    {
        private bool _isSelected;

        /// <summary>
        /// 是否被选中 - 用于复选框绑定
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                OnPropertyChanged(nameof(IsSelected));
            }
        }

        /// <summary>
        /// 工序脚本ID - 使用灵活整数转换器处理可能的字符串或数字类型
        /// </summary>
        [JsonConverter(typeof(FlexibleIntConverter))]
        public int ProcessScriptId { get; set; }
        
        /// <summary>
        /// 工序名称 - 使用灵活字符串转换器处理可能的数字或字符串类型
        /// </summary>
        [JsonConverter(typeof(FlexibleStringConverter))]
        public string ProcessName { get; set; }
        
        /// <summary>
        /// 工序编码 - 使用灵活字符串转换器处理可能的数字或字符串类型
        /// </summary>
        [JsonConverter(typeof(FlexibleStringConverter))]
        public string ProcessCode { get; set; }
        
        /// <summary>
        /// 状态 - 布尔值类型，true表示启用，false表示禁用
        /// </summary>
        public bool Status { get; set; }
        
        /// <summary>
        /// 工序说明 - 使用灵活字符串转换器处理可能的数字或字符串类型
        /// </summary>
        [JsonConverter(typeof(FlexibleStringConverter))]
        public string Description { get; set; }
        
        /// <summary>
        /// 备注信息 - 使用灵活字符串转换器处理可能的数字或字符串类型
        /// </summary>
        [JsonConverter(typeof(FlexibleStringConverter))]
        public string Remark { get; set; }
        
        /// <summary>
        /// 创建时间 - 日期时间类型
        /// </summary>
        public DateTime CreateTime { get; set; }
        
        /// <summary>
        /// 创建者用户ID - 使用灵活字符串转换器处理可能的数字或字符串类型
        /// </summary>
        [JsonConverter(typeof(FlexibleStringConverter))]
        public string CreatorUserId { get; set; }
        
        /// <summary>
        /// 创建者用户名 - 使用灵活字符串转换器处理可能的数字或字符串类型
        /// </summary>
        [JsonConverter(typeof(FlexibleStringConverter))]
        public string CreatorUserName { get; set; }
        
        /// <summary>
        /// 更新者用户名 - 使用灵活字符串转换器处理可能的数字或字符串类型
        /// </summary>
        [JsonConverter(typeof(FlexibleStringConverter))]
        public string UpdateUserName { get; set; }
        
        /// <summary>
        /// 记录ID - 使用灵活字符串转换器处理可能的数字或字符串类型
        /// </summary>
        [JsonConverter(typeof(FlexibleStringConverter))]
        public string Id { get; set; }
        
        // 显示用的格式化属性
        
        /// <summary>
        /// 创建时间显示格式 - 只读属性，格式化为yyyy-MM-dd HH:mm:ss
        /// </summary>
        public string CreateTimeDisplay => CreateTime.ToString("yyyy-MM-dd HH:mm:ss");
        
        /// <summary>
        /// 状态显示文本 - 只读属性，将布尔值转换为中文显示
        /// </summary>
        public string StatusDisplay => Status ? "启用" : "禁用";
        
        /// <summary>
        /// 序号显示 - 基于ProcessScriptId生成的序号
        /// </summary>
        public string SequenceDisplay => ProcessScriptId.ToString();

        /// <summary>
        /// 属性更改事件
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 触发属性更改通知
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
