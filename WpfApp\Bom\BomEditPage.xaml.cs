using System;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;

namespace WpfApp
{
    public partial class BomEditPage : UserControl
    {
        public event Action NavigateBack;
        public event Action<BomProduct> ProductSaved;

        private BomEditViewModel _viewModel;

        public BomEditPage(BomEditViewModel viewModel)
        {
            InitializeComponent();
            _viewModel = viewModel;
            DataContext = viewModel;

            // 订阅保存成功和返回事件
            viewModel.SaveSucceeded += OnSaveSucceeded;
            viewModel.BackRequested += OnBackRequested;

            // 添加调试信息
            this.Loaded += BomEditPage_Loaded;
        }

        private void BomEditPage_Loaded(object sender, RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("=== BomEditPage_Loaded 调试信息 ===");
            System.Diagnostics.Debug.WriteLine($"DataContext 是否为 null: {DataContext == null}");

            if (DataContext is BomEditViewModel viewModel)
            {
                System.Diagnostics.Debug.WriteLine($"DataContext 类型: {DataContext.GetType().Name}");

                // 订阅Materials集合中每个物料的PropertyChanged事件
                foreach (var material in viewModel.Materials)
                {
                    material.PropertyChanged += Material_PropertyChanged;
                }

                // 初始化全选CheckBox状态
                UpdateSelectAllCheckBox();

                // 订阅工艺路线相关属性变化
                viewModel.PropertyChanged += ViewModel_PropertyChanged;

                // 初始化工艺路线显示状态
                UpdateProcessRouteVisibility();

                System.Diagnostics.Debug.WriteLine($"ViewModel.Materials 是否为 null: {viewModel.Materials == null}");
                if (viewModel.Materials != null)
                {
                    System.Diagnostics.Debug.WriteLine($"ViewModel.Materials.Count: {viewModel.Materials.Count}");

                    for (int i = 0; i < viewModel.Materials.Count; i++)
                    {
                        var item = viewModel.Materials[i];
                        System.Diagnostics.Debug.WriteLine($"  物料 {i}: {item.MaterialCode} - {item.MaterialName}");
                    }
                }
            }

            System.Diagnostics.Debug.WriteLine($"MaterialsDataGrid.ItemsSource 是否为 null: {MaterialsDataGrid.ItemsSource == null}");
            if (MaterialsDataGrid.ItemsSource != null)
            {
                System.Diagnostics.Debug.WriteLine($"MaterialsDataGrid.ItemsSource 类型: {MaterialsDataGrid.ItemsSource.GetType().Name}");
            }

            System.Diagnostics.Debug.WriteLine("=== 调试信息结束 ===");
        }

        private void OnSaveSucceeded()
        {
            // 获取保存的产品数据
            var savedProduct = _viewModel.ToProduct();

            // 通知保存成功
            ProductSaved?.Invoke(savedProduct);

            // 保存成功后返回列表页面
            NavigateBack?.Invoke();
        }

        private void Material_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == "IsSelected")
            {
                UpdateSelectAllCheckBox();
            }
        }

        private void SelectAllCheckBox_Click(object sender, RoutedEventArgs e)
        {
            if (sender is CheckBox checkBox && DataContext is BomEditViewModel viewModel)
            {
                bool isChecked = checkBox.IsChecked == true;

                // 设置所有物料的选择状态
                foreach (var material in viewModel.Materials)
                {
                    material.IsSelected = isChecked;
                }

                System.Diagnostics.Debug.WriteLine($"全选/反选完成，设置为: {isChecked}，影响物料数量: {viewModel.Materials.Count}");
            }
        }

        // 更新全选CheckBox的状态（当单个物料选择状态改变时调用）
        public void UpdateSelectAllCheckBox()
        {
            if (DataContext is BomEditViewModel viewModel && SelectAllCheckBox != null)
            {
                var selectedCount = viewModel.Materials.Count(m => m.IsSelected);
                var totalCount = viewModel.Materials.Count;

                if (selectedCount == 0)
                {
                    SelectAllCheckBox.IsChecked = false;
                }
                else if (selectedCount == totalCount)
                {
                    SelectAllCheckBox.IsChecked = true;
                }
                else
                {
                    SelectAllCheckBox.IsChecked = null; // 部分选中状态
                }
            }
        }

        private void OnBackRequested()
        {
            // 返回列表页面
            NavigateBack?.Invoke();
        }

        private void ViewModel_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(BomEditViewModel.SelectedProcessRouteName))
            {
                UpdateProcessRouteVisibility();
            }
            else if (e.PropertyName == nameof(BomEditViewModel.HasProcessSteps))
            {
                System.Diagnostics.Debug.WriteLine($"HasProcessSteps 属性变化: {((BomEditViewModel)sender).HasProcessSteps}");
            }
            else if (e.PropertyName == nameof(BomEditViewModel.SelectedProcessStep))
            {
                var viewModel = sender as BomEditViewModel;
                System.Diagnostics.Debug.WriteLine($"SelectedProcessStep 变化: {viewModel?.SelectedProcessStep?.ProcessStepName}");
            }
        }

        private void UpdateProcessRouteVisibility()
        {
            if (DataContext is BomEditViewModel viewModel)
            {
                System.Diagnostics.Debug.WriteLine($"=== 更新工艺路线显示状态 ===");
                System.Diagnostics.Debug.WriteLine($"SelectedProcessRouteName: '{viewModel.SelectedProcessRouteName}'");
                System.Diagnostics.Debug.WriteLine($"SelectedProcessRouteCode: '{viewModel.SelectedProcessRouteCode}'");

                var noProcessRoutePanel = FindName("NoProcessRoutePanel") as StackPanel;
                var hasProcessRoutePanel = FindName("HasProcessRoutePanel") as StackPanel;

                System.Diagnostics.Debug.WriteLine($"NoProcessRoutePanel 找到: {noProcessRoutePanel != null}");
                System.Diagnostics.Debug.WriteLine($"HasProcessRoutePanel 找到: {hasProcessRoutePanel != null}");

                if (noProcessRoutePanel != null && hasProcessRoutePanel != null)
                {
                    if (string.IsNullOrEmpty(viewModel.SelectedProcessRouteName))
                    {
                        System.Diagnostics.Debug.WriteLine("显示'未选择工艺路线'面板");
                        noProcessRoutePanel.Visibility = Visibility.Visible;
                        hasProcessRoutePanel.Visibility = Visibility.Collapsed;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("显示'已选择工艺路线'面板");
                        noProcessRoutePanel.Visibility = Visibility.Collapsed;
                        hasProcessRoutePanel.Visibility = Visibility.Visible;
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("警告：找不到工艺路线面板控件");
                }
            }
        }
    }

    /// <summary>
    /// 整数到可见性转换器
    /// </summary>
    public class IntToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value is int intValue)
            {
                return intValue > 0 ? Visibility.Visible : Visibility.Collapsed;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
