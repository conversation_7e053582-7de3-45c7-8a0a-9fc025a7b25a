<UserControl x:Class="WpfApp.BomEditPage"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Background="#F5F5F5">

    <UserControl.Resources>
        <!-- Toggle Switch Style -->
        <Style x:Key="ToggleSwitchStyle" TargetType="ToggleButton">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToggleButton">
                        <Border x:Name="Border"
                                CornerRadius="12"
                                Width="50"
                                Height="25"
                                Background="#E0E0E0"
                                BorderThickness="0">
                            <Ellipse x:Name="Thumb"
                                     Width="19"
                                     Height="19"
                                     Fill="White"
                                     HorizontalAlignment="Left"
                                     Margin="3,0,0,0"
                                     RenderTransformOrigin="0.5,0.5">
                                <Ellipse.RenderTransform>
                                    <TranslateTransform x:Name="ThumbTransform" X="0"/>
                                </Ellipse.RenderTransform>
                            </Ellipse>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="ThumbTransform"
                                                           Storyboard.TargetProperty="X"
                                                           To="25" Duration="0:0:0.2"/>
                                            <ColorAnimation Storyboard.TargetName="Border"
                                                          Storyboard.TargetProperty="Background.Color"
                                                          To="#4CAF50" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="ThumbTransform"
                                                           Storyboard.TargetProperty="X"
                                                           To="0" Duration="0:0:0.2"/>
                                            <ColorAnimation Storyboard.TargetName="Border"
                                                          Storyboard.TargetProperty="Background.Color"
                                                          To="#E0E0E0" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="{Binding Title}" FontSize="20" FontWeight="Bold"
                   Margin="0,0,0,20" Foreground="#333"/>

        <!-- 内容区域 - 添加滚动支持 -->
        <ScrollViewer Grid.Row="1"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Auto"
                      Margin="0,0,0,20"
                      Padding="5">
            <StackPanel>
                <!-- 基础信息卡片 -->
                <Border Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,0,0,20">
                    <StackPanel>
                        <!-- 基础信息标题 -->
                        <TextBlock Text="基础信息" FontSize="16" FontWeight="Medium" Foreground="#1976D2"
                                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

                        <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 第一行：BOM编号 和 系统编号开关 -->
                <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,0,20">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                        <TextBlock Text="*" Foreground="Red" FontWeight="Bold" Margin="0,0,5,0"/>
                        <TextBlock Text="BOM编号" FontWeight="Medium" Foreground="#666"/>
                    </StackPanel>
                    <TextBox Text="{Binding BomCode, UpdateSourceTrigger=PropertyChanged}"
                             Height="35" Padding="10,8" FontSize="13"
                             BorderBrush="#DDD" BorderThickness="1"
                             Background="White"/>
                </StackPanel>

                <StackPanel Grid.Row="0" Grid.Column="2" Margin="0,0,0,20">
                    <TextBlock Text="系统编号" FontWeight="Medium" Foreground="#666" Margin="0,0,0,5"/>
                    <ToggleButton IsChecked="{Binding IsSystemCode}"
                                  Height="25" Width="50"
                                  Style="{DynamicResource ToggleSwitchStyle}"/>
                </StackPanel>

                <!-- 第二行：BOM版本 和 默认BOM开关 -->
                <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,0,20">
                    <TextBlock Text="BOM版本" FontWeight="Medium" Foreground="#666" Margin="0,0,0,5"/>
                    <TextBox Text="{Binding BomVersion, UpdateSourceTrigger=PropertyChanged}"
                             Height="35" Padding="10,8" FontSize="13"
                             BorderBrush="#DDD" BorderThickness="1"
                             Background="White"/>
                </StackPanel>

                <StackPanel Grid.Row="1" Grid.Column="2" Margin="0,0,0,20">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                        <TextBlock Text="*" Foreground="Red" FontWeight="Bold" Margin="0,0,5,0"/>
                        <TextBlock Text="默认BOM" FontWeight="Medium" Foreground="#666"/>
                        <TextBlock Text="是" FontWeight="Medium" Foreground="#666" Margin="20,0,10,0"/>
                        <TextBlock Text="否" FontWeight="Medium" Foreground="#666"/>
                    </StackPanel>
                    <ToggleButton IsChecked="{Binding IsDefaultBom}"
                                  Height="25" Width="50"
                                  Style="{DynamicResource ToggleSwitchStyle}"/>
                </StackPanel>

                <!-- 第三行：产品编号 和 产品名称 -->
                <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,0,20">
                    <TextBlock Text="产品编号" FontWeight="Medium" Foreground="#666" Margin="0,0,0,5"/>
                    <TextBox Text="{Binding ProductCode, UpdateSourceTrigger=PropertyChanged}"
                             Height="35" Padding="10,8" FontSize="13"
                             BorderBrush="#DDD" BorderThickness="1"
                             Background="White"/>
                </StackPanel>

                <StackPanel Grid.Row="2" Grid.Column="2" Margin="0,0,0,20">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                        <TextBlock Text="*" Foreground="Red" FontWeight="Bold" Margin="0,0,5,0"/>
                        <TextBlock Text="产品名称" FontWeight="Medium" Foreground="#666"/>
                    </StackPanel>
                    <Grid>
                        <TextBox Text="{Binding ProductId, UpdateSourceTrigger=PropertyChanged}"
                                 Height="35" Padding="10,8,40,8" FontSize="13"
                                 BorderBrush="#DDD" BorderThickness="1"
                                 Background="White" IsReadOnly="True"/>
                        <Button Content="📋" HorizontalAlignment="Right" VerticalAlignment="Center"
                                Width="30" Height="25" Margin="0,0,5,0"
                                Background="Transparent" BorderThickness="0"
                                Foreground="#1976D2" FontSize="12"
                                Command="{Binding SelectProductCommand}"/>
                    </Grid>
                </StackPanel>

                <!-- 第四行：规格型号 和 空 -->
                <StackPanel Grid.Row="3" Grid.Column="0" Margin="0,0,0,20">
                    <TextBlock Text="规格型号" FontWeight="Medium" Foreground="#666" Margin="0,0,0,5"/>
                    <TextBox Text="{Binding Specification, UpdateSourceTrigger=PropertyChanged}"
                             Height="35" Padding="10,8" FontSize="13"
                             BorderBrush="#DDD" BorderThickness="1"
                             Background="White"/>
                </StackPanel>

                <!-- 第五行：单位 和 日产量 -->
                <StackPanel Grid.Row="4" Grid.Column="0" Margin="0,0,0,20">
                    <TextBlock Text="单位" FontWeight="Medium" Foreground="#666" Margin="0,0,0,5"/>
                    <TextBox Text="{Binding Unit, UpdateSourceTrigger=PropertyChanged}"
                             Height="35" Padding="10,8" FontSize="13"
                             BorderBrush="#DDD" BorderThickness="1"
                             Background="White"/>
                </StackPanel>

                <StackPanel Grid.Row="4" Grid.Column="2" Margin="0,0,0,20">
                    <TextBlock Text="日产量" FontWeight="Medium" Foreground="#666" Margin="0,0,0,5"/>
                    <TextBox Text="{Binding DailyOutput, UpdateSourceTrigger=PropertyChanged}"
                             Height="35" Padding="10,8" FontSize="13"
                             BorderBrush="#DDD" BorderThickness="1"
                             Background="White"/>
                </StackPanel>

                <!-- 第六行：备注 (跨两列) -->
                <StackPanel Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="3" Margin="0,0,0,20">
                    <TextBlock Text="备注" FontWeight="Medium" Foreground="#666" Margin="0,0,0,5"/>
                    <TextBox Text="{Binding Remarks, UpdateSourceTrigger=PropertyChanged}"
                             Height="80" Padding="10,8" FontSize="13"
                             BorderBrush="#DDD" BorderThickness="1"
                             Background="White"
                             TextWrapping="Wrap"
                             AcceptsReturn="True"
                             VerticalScrollBarVisibility="Auto"/>
                </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 物料配件区域 -->
                <Border Background="#F8F9FA" CornerRadius="8" Padding="20">
                    <StackPanel>
                        <!-- 物料配件标题 -->
                        <TextBlock Text="物料配件" FontSize="16" FontWeight="Medium" Foreground="#1976D2"
                                   HorizontalAlignment="Center" Margin="0,0,0,20"/>



                        <!-- 操作按钮 -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,0,0,15">
                            <Button Content="添加" Command="{Binding AddMaterialCommand}"
                                    Width="60" Height="30" Margin="0,0,10,0"
                                    Background="#1976D2" BorderThickness="0"
                                    Foreground="White" FontSize="12"/>
                            <Button Content="移除" Command="{Binding RemoveMaterialCommand}"
                                    Width="60" Height="30"
                                    Background="#F44336" BorderThickness="0"
                                    Foreground="White" FontSize="12"/>
                        </StackPanel>

                    <!-- 物料列表 -->
                    <Border BorderBrush="#E0E0E0" BorderThickness="1" Background="White">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- 表头 -->
                            <Border Grid.Row="0" Background="#F8F9FA" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
                                <Grid Height="40">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="50"/>
                                        <ColumnDefinition Width="60"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="100"/>
                                        <ColumnDefinition Width="80"/>
                                        <ColumnDefinition Width="80"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- 全选CheckBox - 使用事件处理而不是数据绑定 -->
                                    <CheckBox Grid.Column="0"
                                              Name="SelectAllCheckBox"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              Click="SelectAllCheckBox_Click"
                                              Margin="5"
                                              IsEnabled="True"
                                              Focusable="True"/>
                                    <TextBlock Grid.Column="1" Text="序号" HorizontalAlignment="Center" VerticalAlignment="Center" FontWeight="Medium"/>
                                    <TextBlock Grid.Column="2" Text="物料名称" HorizontalAlignment="Center" VerticalAlignment="Center" FontWeight="Medium"/>
                                    <TextBlock Grid.Column="3" Text="物料编号" HorizontalAlignment="Center" VerticalAlignment="Center" FontWeight="Medium"/>
                                    <TextBlock Grid.Column="4" Text="规格型号" HorizontalAlignment="Center" VerticalAlignment="Center" FontWeight="Medium"/>
                                    <TextBlock Grid.Column="5" Text="单位" HorizontalAlignment="Center" VerticalAlignment="Center" FontWeight="Medium"/>
                                    <TextBlock Grid.Column="6" Text="数量" HorizontalAlignment="Center" VerticalAlignment="Center" FontWeight="Medium"/>
                                </Grid>
                            </Border>



                            <!-- 数据网格 -->
                            <DataGrid Grid.Row="1" ItemsSource="{Binding Materials}" Name="MaterialsDataGrid"
                                      AutoGenerateColumns="False"
                                      HeadersVisibility="None"
                                      CanUserAddRows="False"
                                      CanUserDeleteRows="False"
                                      SelectionMode="Extended"
                                      GridLinesVisibility="All"
                                      RowHeaderWidth="0"
                                      Background="White"
                                      AlternatingRowBackground="#F5F5F5"
                                      BorderBrush="#CCCCCC"
                                      BorderThickness="1"
                                      FontSize="12"
                                      VerticalScrollBarVisibility="Auto"
                                      HorizontalScrollBarVisibility="Auto"
                                      MinHeight="200">
                                <DataGrid.Columns>
                                    <!-- 选择列 -->
                                    <DataGridTemplateColumn Header="选择">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <CheckBox IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"/>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>

                                    <!-- 序号列 -->
                                    <DataGridTextColumn Header="序号" Binding="{Binding Index}"/>

                                    <!-- 物料名称列 -->
                                    <DataGridTextColumn Header="物料名称" Binding="{Binding MaterialName}"/>

                                    <!-- 物料编号列 -->
                                    <DataGridTextColumn Header="物料编号" Binding="{Binding MaterialCode}"/>

                                    <!-- 规格型号列 -->
                                    <DataGridTextColumn Header="规格型号" Binding="{Binding Specification}"/>

                                    <!-- 单位列 -->
                                    <DataGridTextColumn Header="单位" Binding="{Binding Unit}"/>

                                    <!-- 数量列 -->
                                    <DataGridTextColumn Header="数量" Binding="{Binding Quantity}"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </Border>
                    </StackPanel>
                </Border>

                <!-- 工艺路线区域 -->
                <Border Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,20,0,0">
                    <StackPanel>
                        <!-- 工艺路线标题 -->
                        <TextBlock Text="工艺路线" FontSize="16" FontWeight="Medium" Foreground="#1976D2"
                                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

                        <!-- 工艺路线选择区域 -->
                        <StackPanel HorizontalAlignment="Center" Margin="0,20,0,0">
                            <!-- 未选择工艺路线提示 -->
                            <StackPanel Name="NoProcessRoutePanel" HorizontalAlignment="Center" Margin="0,20,0,20">
                                <!-- 图标 -->
                                <Border Width="60" Height="60" Background="#E3F2FD" CornerRadius="30"
                                        HorizontalAlignment="Center" Margin="0,0,0,15">
                                    <TextBlock Text="📋" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>

                                <!-- 提示文字 -->
                                <TextBlock Text="未选择工艺路线" FontSize="14" Foreground="#666"
                                           HorizontalAlignment="Center" Margin="0,0,0,15"/>

                                <!-- 添加工艺路线按钮 -->
                                <Button Content="添加工艺路线" Command="{Binding SelectProcessRouteCommand}"
                                        Width="120" Height="35"
                                        Background="#1976D2" BorderThickness="0"
                                        Foreground="White" FontWeight="Medium"/>
                            </StackPanel>

                            <!-- 已选择工艺路线显示区域 -->
                            <StackPanel Name="HasProcessRoutePanel" HorizontalAlignment="Center" Margin="0,20,0,20"
                                        Visibility="Collapsed">
                                <!-- 工艺路线信息卡片 -->
                                <Border Background="White" CornerRadius="8" Padding="20" BorderBrush="#E0E0E0" BorderThickness="1"
                                        MinWidth="300">
                                    <StackPanel>
                                        <!-- 工艺路线名称 -->
                                        <TextBlock Text="{Binding SelectedProcessRouteName}" FontSize="16" FontWeight="Medium"
                                                   Foreground="#333" HorizontalAlignment="Center" Margin="0,0,0,10"/>

                                        <!-- 工艺路线编号 -->
                                        <TextBlock Text="{Binding SelectedProcessRouteCode}" FontSize="12"
                                                   Foreground="#666" HorizontalAlignment="Center" Margin="0,0,0,15"/>

                                        <!-- 操作按钮 -->
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Content="更换" Command="{Binding SelectProcessRouteCommand}"
                                                    Width="60" Height="30" Margin="0,0,10,0"
                                                    Background="Transparent" BorderBrush="#1976D2" BorderThickness="1"
                                                    Foreground="#1976D2" FontSize="12"/>
                                            <Button Content="移除" Command="{Binding RemoveProcessRouteCommand}"
                                                    Width="60" Height="30"
                                                    Background="Transparent" BorderBrush="#F44336" BorderThickness="1"
                                                    Foreground="#F44336" FontSize="12"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>
                            </StackPanel>

                            <!-- 警告提示 -->
                            
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- 工序标签页区域 -->
                <Border Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,20,0,0"
                        Visibility="{Binding HasProcessSteps, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel>
                        <!-- 工序标签页标题 -->
                        <TextBlock Text="工序物料配置" FontSize="16" FontWeight="Medium" Foreground="#1976D2"
                                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

                        <!-- 工序标签页 -->
                        <TabControl ItemsSource="{Binding ProcessSteps}" SelectedItem="{Binding SelectedProcessStep}"
                                    Background="Transparent" BorderThickness="0">
                            <TabControl.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Border Width="20" Height="20" Background="#1976D2" CornerRadius="10" Margin="0,0,8,0">
                                            <TextBlock Text="{Binding StepOrder}" FontSize="10" Foreground="White" FontWeight="Bold"
                                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <TextBlock Text="{Binding ProcessStepName}" FontSize="12" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </TabControl.ItemTemplate>
                            <TabControl.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Margin="0,20,0,0">
                                        <!-- 工序信息显示 -->
                                        <Border Background="White" CornerRadius="8" Padding="15" BorderBrush="#E0E0E0" BorderThickness="1" Margin="0,0,0,15">
                                            <StackPanel>
                                                <TextBlock Text="{Binding ProcessStepName}" FontSize="14" FontWeight="Medium" Foreground="#333" Margin="0,0,0,5"/>
                                                <TextBlock Text="{Binding ProcessStepCode}" FontSize="12" Foreground="#666" Margin="0,0,0,5"/>
                                                <TextBlock Text="{Binding Description}" FontSize="11" Foreground="#999"/>
                                            </StackPanel>
                                        </Border>

                                        <!-- 操作按钮区域 -->
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,0,0,15">
                                            <Button Content="添加" Command="{Binding DataContext.AddMaterialCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    Width="60" Height="30" Margin="0,0,10,0"
                                                    Background="#4CAF50" BorderThickness="0"
                                                    Foreground="White" FontSize="12" FontWeight="Medium"/>
                                            <Button Content="移除" Command="{Binding DataContext.RemoveMaterialCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    Width="60" Height="30"
                                                    Background="#F44336" BorderThickness="0"
                                                    Foreground="White" FontSize="12" FontWeight="Medium"/>
                                        </StackPanel>

                                        <!-- 物料列表 -->
                                        <DataGrid ItemsSource="{Binding Materials}"
                                                  AutoGenerateColumns="False" CanUserAddRows="False" CanUserDeleteRows="False"
                                                  GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                                                  Background="White" BorderBrush="#E0E0E0" BorderThickness="1"
                                                  RowHeight="40" FontSize="12">
                                            <DataGrid.Columns>
                                                <!-- 全选CheckBox列 -->
                                                <DataGridTemplateColumn Width="50" CanUserResize="False">
                                                    <DataGridTemplateColumn.Header>
                                                        <CheckBox HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </DataGridTemplateColumn.Header>
                                                    <DataGridTemplateColumn.CellTemplate>
                                                        <DataTemplate>
                                                            <CheckBox IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"
                                                                      HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </DataTemplate>
                                                    </DataGridTemplateColumn.CellTemplate>
                                                </DataGridTemplateColumn>

                                                <!-- 序号列 -->
                                                <DataGridTextColumn Header="序号" Binding="{Binding Index}" Width="60" IsReadOnly="True">
                                                    <DataGridTextColumn.ElementStyle>
                                                        <Style TargetType="TextBlock">
                                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                                        </Style>
                                                    </DataGridTextColumn.ElementStyle>
                                                </DataGridTextColumn>

                                                <!-- 物料编号列 -->
                                                <DataGridTextColumn Header="物料编号" Binding="{Binding MaterialCode}" Width="120" IsReadOnly="True"/>

                                                <!-- 物料名称列 -->
                                                <DataGridTextColumn Header="物料名称" Binding="{Binding MaterialName}" Width="150" IsReadOnly="True"/>

                                                <!-- 规格型号列 -->
                                                <DataGridTextColumn Header="规格型号" Binding="{Binding Specification}" Width="120" IsReadOnly="True"/>

                                                <!-- 单位列 -->
                                                <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="60" IsReadOnly="True"/>

                                                <!-- 使用量列 -->
                                                <DataGridTextColumn Header="使用量" Binding="{Binding UsageAmount}" Width="80"/>

                                                <!-- 用料比例列 -->
                                                <DataGridTextColumn Header="用料比例" Binding="{Binding UsageRatio}" Width="80"/>

                                                <!-- 操作列 -->
                                                <DataGridTemplateColumn Header="操作" Width="120">
                                                    <DataGridTemplateColumn.CellTemplate>
                                                        <DataTemplate>
                                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                                <Button Content="编辑" Width="40" Height="25" Margin="0,0,5,0"
                                                                        Background="Transparent" BorderBrush="#1976D2" BorderThickness="1"
                                                                        Foreground="#1976D2" FontSize="10"/>
                                                                <Button Content="移除" Width="40" Height="25"
                                                                        Background="Transparent" BorderBrush="#F44336" BorderThickness="1"
                                                                        Foreground="#F44336" FontSize="10"
                                                                        Command="{Binding DataContext.DeleteMaterialCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                                        CommandParameter="{Binding}"/>
                                                            </StackPanel>
                                                        </DataTemplate>
                                                    </DataGridTemplateColumn.CellTemplate>
                                                </DataGridTemplateColumn>
                                            </DataGrid.Columns>
                                        </DataGrid>
                                    </StackPanel>
                                </DataTemplate>
                            </TabControl.ContentTemplate>
                        </TabControl>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Content="关闭" Command="{Binding BackCommand}"
                    Width="80" Height="35" Margin="0,0,15,0"
                    Background="White" BorderBrush="#DDD" BorderThickness="1"
                    Foreground="#666" FontWeight="Medium"/>
            <Button Content="确定" Command="{Binding SaveCommand}"
                    Width="80" Height="35"
                    Background="#1976D2" BorderThickness="0"
                    Foreground="White" FontWeight="Medium"/>
        </StackPanel>
    </Grid>
</UserControl>
