using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows.Input;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.Json;

namespace WpfApp.Bom
{
    public class MaterialSelectViewModel : INotifyPropertyChanged
    {
        private bool _isAllSelected;
        private int _currentPage = 1;
        private int _totalCount;
        private int _goToPage = 1;
        private int _pageSize = 10;
        private bool _isLoading;
        private bool _isMaterialTabSelected = true;
        private readonly HttpClient _httpClient;

        public ObservableCollection<MaterialSelectItem> Materials { get; set; }

        public bool IsAllSelected
        {
            get => _isAllSelected;
            set
            {
                _isAllSelected = value;
                OnPropertyChanged(nameof(IsAllSelected));
            }
        }

        public int CurrentPage
        {
            get => _currentPage;
            set
            {
                _currentPage = value;
                OnPropertyChanged(nameof(CurrentPage));
            }
        }

        public int TotalCount
        {
            get => _totalCount;
            set
            {
                _totalCount = value;
                OnPropertyChanged(nameof(TotalCount));
            }
        }

        public int TotalPages { get; private set; }

        public int GoToPage
        {
            get => _goToPage;
            set
            {
                _goToPage = value;
                OnPropertyChanged(nameof(GoToPage));
            }
        }

        public int PageSize
        {
            get => _pageSize;
            set
            {
                _pageSize = value;
                OnPropertyChanged(nameof(PageSize));
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged(nameof(IsLoading));
            }
        }

        public bool IsMaterialTabSelected
        {
            get => _isMaterialTabSelected;
            set
            {
                _isMaterialTabSelected = value;
                OnPropertyChanged(nameof(IsMaterialTabSelected));
                OnPropertyChanged(nameof(IsProductTabSelected));
                if (value)
                {
                    CurrentPage = 1;
                    _ = LoadDataAsync();
                }
            }
        }

        public bool IsProductTabSelected
        {
            get => !_isMaterialTabSelected;
            set
            {
                _isMaterialTabSelected = !value;
                OnPropertyChanged(nameof(IsMaterialTabSelected));
                OnPropertyChanged(nameof(IsProductTabSelected));
                if (value)
                {
                    CurrentPage = 1;
                    _ = LoadDataAsync();
                }
            }
        }

        public ICommand SelectAllCommand { get; }
        public ICommand PreviousPageCommand { get; }
        public ICommand NextPageCommand { get; }
        public ICommand GoToPageCommand { get; }
        public ICommand SelectMaterialTabCommand { get; }
        public ICommand SelectProductTabCommand { get; }

        public MaterialSelectViewModel()
        {
            Materials = new ObservableCollection<MaterialSelectItem>();
            _httpClient = new HttpClient();
            _httpClient.BaseAddress = new System.Uri(ApiConfig.BaseUrl + "/");

            SelectAllCommand = new RelayCommand(_ => SelectAll());
            PreviousPageCommand = new RelayCommand(_ => PreviousPage(), _ => CanPreviousPage());
            NextPageCommand = new RelayCommand(_ => NextPage(), _ => CanNextPage());
            GoToPageCommand = new RelayCommand(_ => GoToSpecificPage());
            SelectMaterialTabCommand = new RelayCommand(_ => IsMaterialTabSelected = true);
            SelectProductTabCommand = new RelayCommand(_ => IsProductTabSelected = true);

            _ = LoadDataAsync();
        }



        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;

                var apiUrl = IsMaterialTabSelected
                    ? $"api/bom/pageMaterial?Page={CurrentPage}&PageSize={PageSize}"
                    : $"api/bom/pageProduct?Page={CurrentPage}&PageSize={PageSize}";

                var response = await _httpClient.GetAsync(apiUrl);

                if (response.IsSuccessStatusCode)
                {
                    var jsonString = await response.Content.ReadAsStringAsync();

                    if (IsMaterialTabSelected)
                    {
                        var apiResponse = JsonSerializer.Deserialize<MaterialPageResponse>(jsonString, new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true,
                            NumberHandling = System.Text.Json.Serialization.JsonNumberHandling.AllowReadingFromString
                        });

                        if (apiResponse != null && apiResponse.Type == "success" && apiResponse.Result != null)
                        {
                            Materials.Clear();

                            var rowNumber = (CurrentPage - 1) * PageSize + 1;
                            if (apiResponse.Result.Items != null)
                            {
                                foreach (var material in apiResponse.Result.Items)
                                {
                                    Materials.Add(new MaterialSelectItem
                                    {
                                        Id = long.TryParse(material.Id, out var id) ? id : 0, // 转换字符串ID为long
                                        RowNumber = rowNumber++,
                                        MaterialCode = material.MaterialCode ?? "",
                                        MaterialName = material.MaterialName ?? "",
                                        Specification = material.Specification ?? "",
                                        Unit = material.Unit ?? "",
                                        MaterialType = material.MaterialType ?? "",
                                        MaterialAttribute = material.MaterialAttribute ?? ""
                                    });
                                }
                            }

                            TotalCount = apiResponse.Result.Total;
                        }
                    }
                    else
                    {
                        var apiResponse = JsonSerializer.Deserialize<ProductPageResponse>(jsonString, new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true,
                            NumberHandling = System.Text.Json.Serialization.JsonNumberHandling.AllowReadingFromString
                        });

                        if (apiResponse != null && apiResponse.Type == "success" && apiResponse.Result != null)
                        {
                            Materials.Clear();

                            var rowNumber = (CurrentPage - 1) * PageSize + 1;
                            if (apiResponse.Result.Items != null)
                            {
                                foreach (var product in apiResponse.Result.Items)
                                {
                                    Materials.Add(new MaterialSelectItem
                                    {
                                        Id = long.TryParse(product.Id, out var id) ? id : 0, // 转换字符串ID为long
                                        RowNumber = rowNumber++,
                                        MaterialCode = product.ProductCode ?? "",
                                        MaterialName = product.ProductName ?? "",
                                        Specification = product.Specification ?? "",
                                        Unit = product.Unit ?? "",
                                        MaterialType = product.ProductType ?? "",
                                        MaterialAttribute = product.ProductAttribute ?? ""
                                    });
                                }
                            }

                            TotalCount = apiResponse.Result.Total;
                        }
                    }
                }
                else
                {
                    // 如果API调用失败，清空数据
                    LoadFallbackData();
                }
            }
            catch (Exception)
            {
                // 如果发生异常，清空数据
                LoadFallbackData();
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void LoadFallbackData()
        {
            // 清空数据
            Materials.Clear();
            TotalCount = 0;
        }

        private void SelectAll()
        {
            foreach (var material in Materials)
            {
                material.IsSelected = IsAllSelected;
            }
        }

        private bool CanPreviousPage()
        {
            return CurrentPage > 1 && !IsLoading;
        }

        private bool CanNextPage()
        {
            return !IsLoading && (TotalCount > CurrentPage * PageSize);
        }

        private async void PreviousPage()
        {
            if (CanPreviousPage())
            {
                CurrentPage--;
                await LoadDataAsync();
            }
        }

        private async void NextPage()
        {
            if (CanNextPage())
            {
                CurrentPage++;
                await LoadDataAsync();
            }
        }

        private async void GoToSpecificPage()
        {
            if (GoToPage >= 1 && GoToPage != CurrentPage && !IsLoading)
            {
                CurrentPage = GoToPage;
                await LoadDataAsync();
            }
        }



        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class MaterialSelectItem : INotifyPropertyChanged
    {
        private bool _isSelected;

        public long Id { get; set; } // 添加物料ID字段
        public int RowNumber { get; set; }
        public string MaterialCode { get; set; } = string.Empty;
        public string MaterialName { get; set; } = string.Empty;
        public string Specification { get; set; } = string.Empty;
        public string Unit { get; set; } = string.Empty;
        public string MaterialType { get; set; } = string.Empty;
        public string MaterialAttribute { get; set; } = string.Empty;

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                OnPropertyChanged(nameof(IsSelected));
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // API响应数据模型
    public class MaterialPageResponse
    {
        public string? Type { get; set; }
        public string? Message { get; set; }
        public MaterialPageResult? Result { get; set; }
    }

    public class MaterialPageResult
    {
        public int PageIndex { get; set; }
        public int Total { get; set; }
        public int TotalPages { get; set; }
        public List<MaterialApiItem>? Items { get; set; }
    }

    public class MaterialApiItem
    {
        public string? Id { get; set; } // 根据JSON数据，ID是字符串类型
        public string? MaterialCode { get; set; }
        public bool IsSystemCode { get; set; }
        public string? MaterialName { get; set; }
        public string? Specification { get; set; }
        public string? Unit { get; set; }
        public string? MaterialType { get; set; }
        public string? MaterialAttribute { get; set; }
        public string? MaterialCategory { get; set; }
        public bool IsEnabled { get; set; }
        public object ValidityDays { get; set; } // 使用object类型避免转换错误
        public object AlarmDays { get; set; } // 使用object类型避免转换错误
        public object StockUpperLimit { get; set; } // 使用object类型避免转换错误
        public object StockLowerLimit { get; set; } // 使用object类型避免转换错误
        public object PurchasePrice { get; set; } // 使用object类型避免转换错误
        public string? Remarks { get; set; }
        public string? ImagePath { get; set; }
        public string? BomID { get; set; }
        public bool IsDelete { get; set; }
        public string? CreateTime { get; set; }
        public string? UpdateTime { get; set; }
        public string? CreateUserId { get; set; }
        public string? CreateUserName { get; set; }
        public string? UpdateUserId { get; set; }
        public string? UpdateUserName { get; set; }
    }

    // 产品API响应数据模型
    public class ProductPageResponse
    {
        public string? Type { get; set; }
        public string? Message { get; set; }
        public ProductPageResult? Result { get; set; }
    }

    public class ProductPageResult
    {
        public int PageIndex { get; set; }
        public int Total { get; set; }
        public int TotalPages { get; set; }
        public List<ProductApiItem>? Items { get; set; }
    }

    public class ProductApiItem
    {
        public string? Id { get; set; } // 根据JSON数据，ID是字符串类型
        public string? ProductCode { get; set; }
        public bool IsSystemCode { get; set; }
        public string? ProductName { get; set; }
        public string? Specification { get; set; }
        public string? Unit { get; set; }
        public string? ProductType { get; set; }
        public string? ProductAttribute { get; set; }
        public string? ProductCategory { get; set; }
        public bool IsEnabled { get; set; }
        public object ValidityDays { get; set; } // 使用object类型避免转换错误
        public object AlarmDays { get; set; } // 使用object类型避免转换错误
        public object StockUpperLimit { get; set; } // 使用object类型避免转换错误
        public object StockLowerLimit { get; set; } // 使用object类型避免转换错误
        public object PurchasePrice { get; set; } // 使用object类型避免转换错误
        public string? Remarks { get; set; }
        public string? ImagePath { get; set; }
        public string? BomID { get; set; }
        public bool IsDelete { get; set; }
        public string? CreateTime { get; set; }
        public string? UpdateTime { get; set; }
        public string? CreateUserId { get; set; }
        public string? CreateUserName { get; set; }
        public string? UpdateUserId { get; set; }
        public string? UpdateUserName { get; set; }
    }
}
