﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WpfApp.WorkTask
{
    public class TaskModel
    {
        public int Index { get; set; }

        public string TaskId { get; set; }

        /// <summary>
        /// 任务编号
        /// </summary>
        public string TaskNumber { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        public string SiteName { get; set; }

        /// <summary>
        /// 工单编号
        /// </summary>
        public string WorkOrderCode { get; set; }

        /// <summary>
        /// 工单名称
        /// </summary>
        public string WorkOrderName { get; set; }

        /// <summary>
        /// 站点编号
        /// </summary>
        public string SiteCode { get; set; }

        /// <summary>
        /// 工序名称
        /// </summary>
        public string ProcessName { get; set; }

        /// <summary>
        /// 工序编号
        /// </summary>
        public string ProcessCode { get; set; }

        /// <summary>
        /// 工艺路线名称
        /// </summary>
        public string ProcessRouteName { get; set; }

        /// <summary>
        /// 任务颜色
        /// </summary>
        public string TaskColor { get; set; }

        /// <summary>
        /// 计划数量
        /// </summary>
        public int PlanQuantity { get; set; }

        /// <summary>
        /// 实际数量
        /// </summary>
        public int RealityQuantity { get; set; }

        /// <summary>
        /// 开工时间
        /// </summary>
        public string StartTime { get; set; }

        /// <summary>
        /// 完工时间
        /// </summary>
        public string EndTime { get; set; }

        /// <summary>
        /// 实际开始时间
        /// </summary>
        public string RealityStartTime { get; set; }

        /// <summary>
        /// 实际结束时间
        /// </summary>
        public string RealityEndTime { get; set; }

        /// <summary>
        /// 工单状态1:派工、2：开工、3：报工
        /// </summary>
        public int TaskStatus { get; set; }

        /// <summary>
        /// 状态文本
        /// </summary>
        public string StatusText { get; set; }
    }
}
