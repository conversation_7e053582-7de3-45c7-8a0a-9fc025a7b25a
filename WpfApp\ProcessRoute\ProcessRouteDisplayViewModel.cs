using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Net.Http;
using System.Runtime.CompilerServices;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using WpfApp.Common;
using System.Linq;

namespace WpfApp.ProcessRoute
{
    /// <summary>
    /// 工艺路线显示视图模型类
    /// </summary>
    public class ProcessRouteDisplayViewModel : INotifyPropertyChanged
    {
        /// <summary>
        /// 请求导航到新增工艺路线页面的事件
        /// </summary>
        public static event Action RequestNavigateToAddProcessRoute;
        private readonly HttpClient _httpClient;
        private ObservableCollection<ProcessRouteDisplayModel> _processRoutes;
        private ProcessRouteDisplayModel _selectedProcessRoute;
        private int _currentPage = 1;
        private int _pageSize = 10;
        private int _totalCount = 0;
        private bool _isAllSelected = false;
        private bool _isUpdatingSelection = false;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public ProcessRouteDisplayViewModel()
        {
            // 初始化HTTP客户端
            _httpClient = new HttpClient();
            
            // 初始化工艺路线集合
            ProcessRoutes = new ObservableCollection<ProcessRouteDisplayModel>();
            
            // 初始化命令
            InitializeCommands();

            // 添加测试数据
            AddTestData();

            // 加载数据
            LoadDataAsync();
        }

        /// <summary>
        /// 工艺路线数据集合
        /// </summary>
        public ObservableCollection<ProcessRouteDisplayModel> ProcessRoutes
        {
            get => _processRoutes;
            set
            {
                // 如果之前有数据，先取消事件订阅
                if (_processRoutes != null)
                {
                    foreach (var item in _processRoutes)
                    {
                        item.PropertyChanged -= OnProcessRouteSelectionChanged;
                    }
                }
                
                _processRoutes = value;
                
                // 为新数据订阅事件
                if (_processRoutes != null)
                {
                    foreach (var item in _processRoutes)
                    {
                        item.PropertyChanged += OnProcessRouteSelectionChanged;
                    }
                }
                
                OnPropertyChanged();
                UpdateIsAllSelected();
            }
        }

        /// <summary>
        /// 当前选中的工艺路线
        /// </summary>
        public ProcessRouteDisplayModel SelectedProcessRoute
        {
            get => _selectedProcessRoute;
            set
            {
                _selectedProcessRoute = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int CurrentPage
        {
            get => _currentPage;
            set
            {
                _currentPage = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CurrentPageDisplay));
            }
        }

        /// <summary>
        /// 每页显示数量
        /// </summary>
        public int PageSize
        {
            get => _pageSize;
            set
            {
                _pageSize = value;
                OnPropertyChanged();
                LoadDataAsync();
            }
        }

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount
        {
            get => _totalCount;
            set
            {
                _totalCount = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 当前页显示文本
        /// </summary>
        public string CurrentPageDisplay => $"第 {CurrentPage} 页";

        /// <summary>
        /// 是否全选
        /// </summary>
        public bool IsAllSelected
        {
            get => _isAllSelected;
            set
            {
                if (_isAllSelected != value)
                {
                    _isAllSelected = value;
                    OnPropertyChanged();
                    
                    // 更新所有项目的选择状态
                    _isUpdatingSelection = true;
                    try
                    {
                        if (ProcessRoutes != null)
                        {
                            foreach (var item in ProcessRoutes)
                            {
                                item.IsSelected = value;
                            }
                        }
                    }
                    finally
                    {
                        _isUpdatingSelection = false;
                    }
                }
            }
        }

        // 命令属性
        public ICommand AddCommand { get; private set; }
        public ICommand EditCommand { get; private set; }
        public ICommand DeleteCommand { get; private set; }
        public ICommand DeleteSelectedCommand { get; private set; }
        public ICommand RefreshCommand { get; private set; }
        public ICommand ExportCommand { get; private set; }
        public ICommand PrevPageCommand { get; private set; }
        public ICommand NextPageCommand { get; private set; }

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            AddCommand = new WpfApp.Bom.RelayCommand(_ => ExecuteAdd());
            EditCommand = new WpfApp.Bom.RelayCommand(param => ExecuteEdit(param as ProcessRouteDisplayModel));
            DeleteCommand = new WpfApp.Bom.RelayCommand(param => ExecuteDelete(param as ProcessRouteDisplayModel));
            DeleteSelectedCommand = new WpfApp.Bom.RelayCommand(_ => ExecuteDeleteSelected());
            RefreshCommand = new WpfApp.Bom.RelayCommand(_ => LoadDataAsync());
            ExportCommand = new WpfApp.Bom.RelayCommand(_ => ExecuteExport());
            PrevPageCommand = new WpfApp.Bom.RelayCommand(_ => ExecutePrevPage(), _ => CurrentPage > 1);
            NextPageCommand = new WpfApp.Bom.RelayCommand(_ => ExecuteNextPage(), _ => ProcessRoutes.Count >= PageSize);
        }

        /// <summary>
        /// 加载工艺路线数据
        /// </summary>
        private async Task LoadDataAsync()
        {
            try
            {
                using var client = new HttpClient();
                
                var url = "http://localhost:5005/api/processRoute/processRouteList";
                var response = await client.GetAsync(url);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };
                    
                    var apiResult = JsonSerializer.Deserialize<ApiResult<List<ProcessRouteDisplayModel>>>(content, options);
                    
                    if (apiResult?.Code == 200 && apiResult.Result != null)
                    {
                        var allItems = apiResult.Result;
                        
                        ProcessRoutes.Clear();
                        TotalCount = allItems.Count;
                        
                        // 设置行号
                        for (int i = 0; i < allItems.Count; i++)
                        {
                            allItems[i].RowNumber = i + 1;
                        }
                        
                        var pageItems = allItems
                            .Skip((CurrentPage - 1) * PageSize)
                            .Take(PageSize)
                            .ToList();
                            
                        foreach (var item in pageItems)
                        {
                            // 订阅选择状态变化事件
                            item.PropertyChanged += OnProcessRouteSelectionChanged;
                            ProcessRoutes.Add(item);
                        }
                        
                        // 更新全选状态
                        UpdateIsAllSelected();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载工艺路线数据时发生错误: {ex.Message}", "异常",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 处理工艺路线选择状态变化
        /// </summary>
        private void OnProcessRouteSelectionChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(ProcessRouteDisplayModel.IsSelected) && !_isUpdatingSelection)
            {
                UpdateIsAllSelected();
            }
        }

        /// <summary>
        /// 更新全选状态
        /// </summary>
        private void UpdateIsAllSelected()
        {
            if (ProcessRoutes == null || ProcessRoutes.Count == 0)
            {
                _isAllSelected = false;
            }
            else
            {
                _isAllSelected = ProcessRoutes.All(p => p.IsSelected);
            }
            OnPropertyChanged(nameof(IsAllSelected));
        }
        
        // 命令执行方法
        private void ExecuteAdd()
        {
            try
            {
                // 调试信息
                MessageBox.Show("新增按钮被点击了！", "调试", MessageBoxButton.OK, MessageBoxImage.Information);

                // 触发导航到新增工艺路线页面的事件
                RequestNavigateToAddProcessRoute?.Invoke();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导航到新增页面时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void ExecuteEdit(ProcessRouteDisplayModel route)
        {
            if (route != null)
            {
                MessageBox.Show($"编辑工艺路线: {route.ProcessRouteName}，ID: {route.Id}", "提示", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        
        private void ExecuteDelete(ProcessRouteDisplayModel route)
        {
            if (route != null)
            {
                var result = MessageBox.Show($"确定要删除工艺路线 \"{route.ProcessRouteName}\" 吗?", "确认删除", 
                    MessageBoxButton.YesNo, MessageBoxImage.Question);
                    
                if (result == MessageBoxResult.Yes)
                {
                    // 实际删除逻辑待实现
                    MessageBox.Show("删除功能开发中", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }
        
        private void ExecuteDeleteSelected()
        {
            var selectedItems = ProcessRoutes.Where(p => p.IsSelected).ToList();
            if (selectedItems.Any())
            {
                var result = MessageBox.Show($"确定要删除选中的 {selectedItems.Count} 条工艺路线记录吗?", "确认删除", 
                    MessageBoxButton.YesNo, MessageBoxImage.Question);
                    
                if (result == MessageBoxResult.Yes)
                {
                    // 实际删除逻辑待实现
                    MessageBox.Show("批量删除功能开发中", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            else
            {
                MessageBox.Show("请至少选择一条记录", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        
        private void ExecuteExport()
        {
            MessageBox.Show("导出功能开发中", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        private void ExecutePrevPage()
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
                LoadDataAsync();
            }
        }
        
        private void ExecuteNextPage()
        {
            CurrentPage++;
            LoadDataAsync();
        }

        /// <summary>
        /// 添加测试数据
        /// </summary>
        private void AddTestData()
        {
            ProcessRoutes.Add(new ProcessRouteDisplayModel
            {
                ProcessRouteCode = "GYLN0001",
                ProcessRouteName = "测试工艺路线1",
                Status = true,
                Remarks = "测试备注1",
                RowNumber = 1
            });

            ProcessRoutes.Add(new ProcessRouteDisplayModel
            {
                ProcessRouteCode = "GYLN0002",
                ProcessRouteName = "工艺",
                Status = true,
                Remarks = "null",
                RowNumber = 2
            });

            ProcessRoutes.Add(new ProcessRouteDisplayModel
            {
                ProcessRouteCode = "GYLN0003",
                ProcessRouteName = "高级工艺路线",
                Status = false,
                Remarks = "高级工艺路线备注",
                RowNumber = 3
            });


        }

        /// <summary>
        /// 属性变更事件
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 触发属性变更通知
        /// </summary>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
