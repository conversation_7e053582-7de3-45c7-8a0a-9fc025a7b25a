namespace WpfApp.Procedure
{
    /// <summary>
    /// API响应结果通用类
    /// </summary>
    /// <typeparam name="T">响应数据类型</typeparam>
    public class ApiResult<T>
    {
        /// <summary>
        /// 响应状态码
        /// </summary>
        public int Code { get; set; }

        /// <summary>
        /// 响应类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 响应消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 响应结果数据
        /// </summary>
        public T Result { get; set; }
    }
} 