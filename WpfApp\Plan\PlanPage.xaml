<UserControl x:Class="WpfApp.PlanPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:WpfApp.Plan"
             xmlns:sys="clr-namespace:System;assembly=mscorlib"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             >
    <Grid Background="#F5F5F5">
        <!-- 查询区 -->
        <Border Background="White" VerticalAlignment="Top" Margin="10,10,10,0" BorderThickness="1" BorderBrush="#EEEEEE" CornerRadius="4">
            <Grid>
                <!-- 查询条件 -->
                <Grid Margin="10" x:Name="SearchGrid">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 第一行 -->
                    <!-- 计划编号/名称 -->
                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,15,15">
                        <TextBlock Text="计划编号/名称" Foreground="#303133" FontSize="14" Margin="0,0,0,5"/>
                        <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                            <TextBox Text="{Binding Keyword, UpdateSourceTrigger=PropertyChanged}"
                                     BorderThickness="0"
                                     Padding="8,6"
                                     FontSize="13"
                                     Foreground="#606266"
                                     materialDesign:HintAssist.Hint="请输入计划编号/名称"
                                     materialDesign:HintAssist.Foreground="#C0C4CC"
                                     Background="Transparent"/>
                        </Border>
                    </StackPanel>

                    <!-- 订单来源 -->
                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="0,0,15,15">
                        <TextBlock Text="订单来源" Foreground="#303133" FontSize="14" Margin="0,0,0,5"/>
                        <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                            <ComboBox ItemsSource="{Binding SourceTypes}"
                                      SelectedItem="{Binding SelectedSourceType}"
                                      DisplayMemberPath="Name"
                                      BorderThickness="0"
                                      Padding="8,6"
                                      FontSize="13"
                                      Foreground="#606266"
                                      materialDesign:HintAssist.Hint="请选择订单来源"
                                      materialDesign:HintAssist.Foreground="#C0C4CC"
                                      Background="Transparent">
                                <ComboBox.Resources>
                                    <SolidColorBrush x:Key="MaterialDesignPaper" Color="White"/>
                                </ComboBox.Resources>
                            </ComboBox>
                        </Border>
                    </StackPanel>

                    <!-- 产品名称 -->
                    <StackPanel Grid.Row="0" Grid.Column="2" Margin="0,0,15,15">
                        <TextBlock Text="产品名称" Foreground="#303133" FontSize="14" Margin="0,0,0,5"/>
                        <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                            <TextBox Text="{Binding ProductName, UpdateSourceTrigger=PropertyChanged}"
                                     BorderThickness="0"
                                     Padding="8,6"
                                     FontSize="13"
                                     Foreground="#606266"
                                     materialDesign:HintAssist.Hint="请输入产品名称"
                                     materialDesign:HintAssist.Foreground="#C0C4CC"
                                     Background="Transparent"/>
                        </Border>
                    </StackPanel>

                    <!-- 状态 -->
                    <StackPanel Grid.Row="0" Grid.Column="3" Margin="0,0,15,15">
                        <TextBlock Text="状态" Foreground="#303133" FontSize="14" Margin="0,0,0,5"/>
                        <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                            <ComboBox ItemsSource="{Binding StatusTypes}"
                                      SelectedItem="{Binding SelectedStatusType}"
                                      DisplayMemberPath="Name"
                                      BorderThickness="0"
                                      Padding="8,6"
                                      FontSize="13"
                                      Foreground="#606266"
                                      materialDesign:HintAssist.Hint="全部"
                                      materialDesign:HintAssist.Foreground="#C0C4CC"
                                      Background="Transparent">
                                <ComboBox.Resources>
                                    <SolidColorBrush x:Key="MaterialDesignPaper" Color="White"/>
                                </ComboBox.Resources>
                            </ComboBox>
                        </Border>
                    </StackPanel>

                    <!-- 第二行 -->
                    <!-- 计划开始时间 -->
                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,15,15">
                        <TextBlock Text="计划开始时间" Foreground="#303133" FontSize="14" Margin="0,0,0,5"/>
                        <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                            <DatePicker SelectedDate="{Binding PlanStartDate}"
                                        BorderThickness="0"
                                        Padding="8,6"
                                        FontSize="13"
                                        Foreground="#606266"
                                        materialDesign:HintAssist.Hint="请选择计划开始时间"
                                        materialDesign:HintAssist.Foreground="#C0C4CC"
                                        Background="Transparent"/>
                        </Border>
                    </StackPanel>

                    <!-- 计划结束时间 -->
                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="0,0,15,15">
                        <TextBlock Text="计划结束时间" Foreground="#303133" FontSize="14" Margin="0,0,0,5"/>
                        <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                            <DatePicker SelectedDate="{Binding PlanEndDate}"
                                        BorderThickness="0"
                                        Padding="8,6"
                                        FontSize="13"
                                        Foreground="#606266"
                                        materialDesign:HintAssist.Hint="请选择计划结束时间"
                                        materialDesign:HintAssist.Foreground="#C0C4CC"
                                        Background="Transparent"/>
                        </Border>
                    </StackPanel>

                    <!-- 需求日期 -->
                    <StackPanel Grid.Row="1" Grid.Column="2" Margin="0,0,15,15">
                        <TextBlock Text="需求日期" Foreground="#303133" FontSize="14" Margin="0,0,0,5"/>
                        <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                            <Grid>
                                <DatePicker SelectedDate="{Binding DemandDate}"
                                            BorderThickness="0"
                                            Padding="8,6"
                                            FontSize="13"
                                            Foreground="#606266"
                                            materialDesign:HintAssist.Hint="请选择需求日期"
                                            materialDesign:HintAssist.Foreground="#C0C4CC"
                                            Background="Transparent"
                                            IsDropDownOpen="{Binding IsDemandDatePickerOpen, Mode=TwoWay}"/>
                                <Button HorizontalAlignment="Right" Width="30" Height="30" Padding="0" Margin="0,0,5,0"
                                        Command="{Binding ToggleDemandDatePickerCommand}"
                                        Background="Transparent" BorderThickness="0">
                                    <materialDesign:PackIcon Kind="Calendar" Foreground="#409EFF" Width="16" Height="16"/>
                                </Button>
                            </Grid>
                        </Border>
                    </StackPanel>

                    <!-- 按钮区域 -->
                    <StackPanel Grid.Row="0" Grid.RowSpan="2" Grid.Column="4" Orientation="Vertical" VerticalAlignment="Bottom" Margin="0,0,0,15">
                        <Button Content="重置" Command="{Binding ResetCommand}"
                                Background="White" Foreground="#409EFF"
                                BorderBrush="#409EFF" BorderThickness="1"
                                Margin="0,0,0,10" Width="80" Height="32"
                                FontSize="13">
                            <Button.Resources>
                                <Style TargetType="Border">
                                    <Setter Property="CornerRadius" Value="2"/>
                                </Style>
                            </Button.Resources>
                        </Button>
                        <Button Content="查询" Command="{Binding QueryCommand}"
                                Background="#409EFF" Foreground="White"
                                BorderBrush="#409EFF" BorderThickness="0"
                                Width="80" Height="32"
                                FontSize="13">
                            <Button.Resources>
                                <Style TargetType="Border">
                                    <Setter Property="CornerRadius" Value="2"/>
                                </Style>
                            </Button.Resources>
                        </Button>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>

        <!-- 数据列表 -->
        <Border Background="White" Margin="10,180,10,50" BorderThickness="1" BorderBrush="#EEEEEE" CornerRadius="4">
            <Grid>
                <!-- 按钮区域 -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="15,15,0,0">
                    <!-- 新增按钮 -->
                    <Button Content="新增" 
                            Command="{Binding AddNewPlanCommand}"
                            Background="#409EFF" Foreground="White"
                            BorderThickness="0" Width="80" Height="32" FontSize="14" Margin="0,0,10,0">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="2"/>
                            </Style>
                        </Button.Resources>
                    </Button>
                    
                    <!-- 编辑按钮 -->
                    <Button Content="编辑" 
                            Command="{Binding EditSelectedPlanCommand}"
                            Background="White" Foreground="#409EFF"
                            BorderBrush="#409EFF" BorderThickness="1" Width="80" Height="32" FontSize="14" Margin="0,0,10,0">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="2"/>
                            </Style>
                        </Button.Resources>
                    </Button>
                    
                    <!-- 删除按钮 -->
                    <Button Content="删除" 
                            Command="{Binding DeleteSelectedPlanCommand}"
                            Background="White" Foreground="#F56C6C"
                            BorderBrush="#F56C6C" BorderThickness="1" Width="80" Height="32" FontSize="14">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="2"/>
                            </Style>
                        </Button.Resources>
                    </Button>
                </StackPanel>

                <DataGrid ItemsSource="{Binding Plans}" AutoGenerateColumns="False" Margin="0,60,0,0"
                          Name="PlansDataGrid" CanUserAddRows="False" CanUserDeleteRows="False"
                          EnableRowVirtualization="True" VirtualizingPanel.IsVirtualizing="True"
                          VirtualizingPanel.VirtualizationMode="Recycling"
                          HeadersVisibility="Column"
                          GridLinesVisibility="Horizontal"
                          BorderThickness="0"
                          Background="White"
                          RowBackground="White"
                          AlternatingRowBackground="#F9F9F9"
                          SelectionMode="Extended"
                          SelectionUnit="FullRow"
                          SelectionChanged="PlansDataGrid_SelectionChanged">
                    <DataGrid.Resources>
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background" Value="White"/>
                            <Setter Property="Padding" Value="10,5"/>
                            <Setter Property="BorderThickness" Value="0,0,0,1"/>
                            <Setter Property="BorderBrush" Value="#EEEEEE"/>
                        </Style>
                        <Style TargetType="DataGridRow">
                            <Setter Property="Height" Value="40"/>
                        </Style>
                    </DataGrid.Resources>
                    <DataGrid.Columns>
                        <!-- 选择列 -->
                        <DataGridCheckBoxColumn Width="40" Binding="{Binding IsSelected, Mode=TwoWay}">
                            <DataGridCheckBoxColumn.Header>
                                <Border Width="16" Height="16" Background="#673AB7" CornerRadius="2">
                                    <CheckBox IsChecked="{Binding DataContext.IsAllSelected, RelativeSource={RelativeSource AncestorType=DataGrid}, Mode=TwoWay}"
                                              Opacity="0"
                                              Cursor="Hand"
                                              ToolTip="全选/取消全选"/>
                                </Border>
                            </DataGridCheckBoxColumn.Header>
                            <DataGridCheckBoxColumn.ElementStyle>
                                <Style TargetType="CheckBox">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <EventSetter Event="Checked" Handler="CheckBox_Checked"/>
                                    <EventSetter Event="Unchecked" Handler="CheckBox_Unchecked"/>
                                </Style>
                            </DataGridCheckBoxColumn.ElementStyle>
                            <DataGridCheckBoxColumn.EditingElementStyle>
                                <Style TargetType="CheckBox">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <EventSetter Event="Checked" Handler="CheckBox_Checked"/>
                                    <EventSetter Event="Unchecked" Handler="CheckBox_Unchecked"/>
                                </Style>
                            </DataGridCheckBoxColumn.EditingElementStyle>
                        </DataGridCheckBoxColumn>

                        <!-- 序号列 -->
                        <DataGridTextColumn Header="序号" Width="60" Binding="{Binding Tag, RelativeSource={RelativeSource AncestorType=DataGridRow}}" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 计划编号 -->
                        <DataGridTemplateColumn Header="计划编号" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding PlanCode}" Foreground="#1890FF" Cursor="Hand" VerticalAlignment="Center"
                                               TextDecorations="Underline" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTextColumn Header="计划名称" Binding="{Binding PlanName}" Width="120"/>
                        <DataGridTextColumn Header="工单数量" Binding="{Binding WorkOrderNumber}" Width="80"/>
                        <DataGridTextColumn Header="来源类型" Binding="{Binding SourceName}" Width="100"/>
                        <DataGridTextColumn Header="成品名称" Binding="{Binding ProductName}" Width="120"/>
                        <DataGridTextColumn Header="成品编号" Binding="{Binding ProductCode}" Width="120"/>
                        <DataGridTextColumn Header="规格型号" Binding="{Binding Specification}" Width="100"/>
                        <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="60"/>
                        <DataGridTextColumn Header="计划数量" Binding="{Binding PlanNumber}" Width="80"/>

                        <!-- 计划开始时间 -->
                        <DataGridTextColumn Header="计划开工时间" Width="120">
                            <DataGridTextColumn.Binding>
                                <Binding Path="PlanStartTime" StringFormat="{}{0:yyyy-MM-dd}">
                                    <Binding.TargetNullValue>
                                        <sys:String>--</sys:String>
                                    </Binding.TargetNullValue>
                                </Binding>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- 计划结束时间 -->
                        <DataGridTextColumn Header="计划完工时间" Width="120">
                            <DataGridTextColumn.Binding>
                                <Binding Path="PlanEndTime" StringFormat="{}{0:yyyy-MM-dd}">
                                    <Binding.TargetNullValue>
                                        <sys:String>--</sys:String>
                                    </Binding.TargetNullValue>
                                </Binding>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- 需求日期 -->
                        <DataGridTextColumn Header="需求日期" Width="120">
                            <DataGridTextColumn.Binding>
                                <Binding Path="DemandTime" StringFormat="{}{0:yyyy-MM-dd}">
                                    <Binding.TargetNullValue>
                                        <sys:String>--</sys:String>
                                    </Binding.TargetNullValue>
                                </Binding>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="状态" Binding="{Binding PlanStatusName}" Width="80"/>

                        <!-- 操作列 -->
                        <DataGridTemplateColumn Header="操作" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="分解" 
                                                Command="{Binding DataContext.DecomposeCommand, RelativeSource={RelativeSource FindAncestor, AncestorType=DataGrid}}"
                                                CommandParameter="{Binding}" 
                                                Margin="2" Padding="5,0" Height="24"
                                                Background="#1890FF" Foreground="White" BorderThickness="0"
                                                Visibility="{Binding CanDecompose, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                        <Button Content="撤回" 
                                                Command="{Binding DataContext.WithdrawCommand, RelativeSource={RelativeSource FindAncestor, AncestorType=DataGrid}}"
                                                CommandParameter="{Binding}" 
                                                Margin="2" Padding="5,0" Height="24"
                                                Background="#1890FF" Foreground="White" BorderThickness="0"
                                                Visibility="{Binding CanWithdraw, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                        <Button Content="编辑" 
                                                Command="{Binding DataContext.EditCommand, RelativeSource={RelativeSource FindAncestor, AncestorType=DataGrid}}"
                                                CommandParameter="{Binding}" 
                                                Margin="2" Padding="5,0" Height="24"
                                                Background="#1890FF" Foreground="White" BorderThickness="0"
                                                Visibility="{Binding CanEdit, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- 分页控件 -->
        <Grid VerticalAlignment="Bottom" Margin="10,0,10,10" Height="40">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧信息 -->
            <TextBlock Grid.Column="0" Text="{Binding TotalInfoText}" VerticalAlignment="Center" Foreground="#666666"/>

            <!-- 中间分页按钮 -->
            <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="&lt;" Command="{Binding PrevPageCommand}" Margin="5,0"
                        Background="White" Foreground="#333333"
                        BorderBrush="#DDDDDD" BorderThickness="1" Width="30" Height="30"/>

                <ItemsControl ItemsSource="{Binding PageButtons}">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <StackPanel Orientation="Horizontal"/>
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Background="{Binding IsSelected, Converter={StaticResource SelectedPageBackgroundConverter}}" 
                                    BorderBrush="#DDDDDD" BorderThickness="1" Margin="2,0" Width="30" Height="30">
                                <TextBlock Text="{Binding PageNumber}" 
                                           Foreground="{Binding IsSelected, Converter={StaticResource SelectedPageForegroundConverter}}" 
                                           HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>

                <Button Content="&gt;" Command="{Binding NextPageCommand}" Margin="5,0"
                        Background="White" Foreground="#333333"
                        BorderBrush="#DDDDDD" BorderThickness="1" Width="30" Height="30"/>
            </StackPanel>

            <!-- 右侧页码选择 -->
            <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center">
                <ComboBox Width="80" SelectedItem="{Binding PageSize}" ItemsSource="{Binding PageSizeOptions}"
                          BorderBrush="#DDDDDD" BorderThickness="1" Padding="5,0">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Converter={StaticResource PageSizeConverter}}"/>
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>

                <Button Content="前往" Margin="10,0,0,0" Width="60" Height="30"
                        Background="White" Foreground="#333333"
                        BorderBrush="#DDDDDD" BorderThickness="1"/>

                <TextBox Width="60" Margin="10,0,0,0" Text="{Binding CurrentPageText, UpdateSourceTrigger=PropertyChanged}"
                         BorderBrush="#DDDDDD" BorderThickness="1" Padding="5,0" VerticalContentAlignment="Center"/>

                <TextBlock Text="页" VerticalAlignment="Center" Margin="5,0,0,0"/>
            </StackPanel>
        </Grid>

        <!-- 日历选择器弹出层 -->
        <Popup x:Name="CalendarPopup" IsOpen="{Binding IsCalendarPopupOpen}" StaysOpen="False"
               PlacementTarget="{Binding ElementName=PlansDataGrid}" Placement="Center">
            <Border Background="White" BorderBrush="#DDDDDD" BorderThickness="1" Padding="10">
                <Calendar SelectedDate="{Binding SelectedCalendarDate, Mode=TwoWay}"/>
            </Border>
        </Popup>

        <!-- 新增对话框 -->
        <Grid Visibility="{Binding IsAddDialogOpen, Converter={StaticResource BooleanToVisibilityConverter}}"
              HorizontalAlignment="Stretch" VerticalAlignment="Stretch"
              Panel.ZIndex="100">
            <!-- 叠加层：先遮罩，后内容 -->
            <Border Background="Black" Opacity="0.5"/>
            <local:AddPlanDialog HorizontalAlignment="Left" VerticalAlignment="Top" Margin="370,10,0,0" Loaded="AddPlanDialog_Loaded"/>
        </Grid>
    </Grid>
</UserControl> 