using System.Collections.Generic;
using System.Windows;

namespace WpfApp.ProcessRoute
{
    /// <summary>
    /// 产品多选对话框
    /// </summary>
    public partial class ProductMultiSelectDialog : Window
    {
        public List<ProductSelectItem> SelectedProducts { get; private set; }

        public ProductMultiSelectDialog()
        {
            InitializeComponent();
            DataContext = new ProductMultiSelectViewModel();
            
            // 订阅ViewModel的关闭事件
            if (DataContext is ProductMultiSelectViewModel viewModel)
            {
                viewModel.CloseRequested += (result, selectedProducts) =>
                {
                    if (result)
                    {
                        SelectedProducts = selectedProducts;
                        DialogResult = true;
                    }
                    else
                    {
                        DialogResult = false;
                    }
                    Close();
                };
            }
        }

        public ProductMultiSelectDialog(ProductMultiSelectViewModel viewModel) : this()
        {
            DataContext = viewModel;
            
            // 订阅ViewModel的关闭事件
            viewModel.CloseRequested += (result, selectedProducts) =>
            {
                if (result)
                {
                    SelectedProducts = selectedProducts;
                    DialogResult = true;
                }
                else
                {
                    DialogResult = false;
                }
                Close();
            };
        }
    }
}
