﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WpfApp.WorkOrder
{
    public class WorkOrderModel
    {
        public int RowIndex { get; set; }
        public long WorkOrderId { get; set;}
        /// <summary>
        /// 工单编号
        /// </summary>
        public string? WorkOrderCode { get; set; }
        /// <summary>
        /// 工单名称
        /// </summary>
        public string? WorkOrderName { get; set; }
        /// <summary>
        /// 工序步骤列表
        /// </summary>
        [System.Text.Json.Serialization.JsonIgnore]
        public List<ProcessStep> ProcessSteps { get; set; } = new List<ProcessStep>();

        /// <summary>
        /// 工序ID字符串（用于接收API数据，格式如"2,3"）
        /// </summary>
        [System.Text.Json.Serialization.JsonPropertyName("processSteps")]
        public string? ProcessStepIds { get; set; }

        /// <summary>
        /// 进度百分比
        /// </summary>
        public int ProgressPercentage { get; set; }
        /// <summary>
        /// BOMId
        /// </summary>
        public long? BomId { get; set; }
        /// <summary>
        /// 关联计划
        /// </summary>
        public string? PlanName { get; set; }
        /// <summary>
        /// 产品Id
        /// </summary>
        public long ProductId { get; set; }
        /// <summary>
        /// 产品编号（唯一标识）312312213
        /// </summary>
        public string? ProductCode { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 产品规格型号
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 产品计量单位（个/件/箱等）
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// 产品类型（下拉选项值）
        /// </summary>
        public string? ProductType { get; set; }

        /// <summary>
        /// 计划开工时间
        /// </summary>
        public string? PlanStartTime { get; set; }
        /// <summary>
        /// 实际开工时间
        /// </summary>
        public string? RealityStartTime { get; set; }
        /// <summary>
        /// 计划完工时间
        /// </summary>
        public string? PlanEndTime { get; set; }
        /// <summary>
        /// 实际完工时间
        /// </summary>
        public string? RealityEndTime { get; set; }
        /// <summary>
        /// 需求时间
        /// </summary>
        public string? DemandTime { get; set; }
        /// <summary>
        /// 计划数量
        /// </summary>
        public int? PlanNumber { get; set; }
        /// <summary>
        /// 实际数量
        /// </summary>
        public int? RealityNumber { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }
        /// <summary>
        /// 状态
        /// 0:待排产
        /// 1:未开始
        /// 2:进行中
        /// 3:已完成
        /// 4:已撤回
        /// 5:已取消
        /// </summary>
        public int Status { get; set; }
    }
    public class ProcessStep
    {
        public string StepName { get; set; }
        public bool IsCompleted { get; set; }
    }
}
