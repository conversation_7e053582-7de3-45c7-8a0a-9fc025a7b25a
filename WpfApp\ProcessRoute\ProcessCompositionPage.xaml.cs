// 引用WPF控件类库
using System.Windows.Controls;

// 定义工艺路线命名空间
namespace WpfApp.ProcessRoute
{
    /// <summary>
    /// 工艺路线组成页面的部分类
    /// 继承自UserControl，用于显示工艺路线组成管理界面
    /// </summary>
    public partial class ProcessCompositionPage : UserControl
    {
        /// <summary>
        /// 构造函数 - 初始化工艺路线组成页面
        /// </summary>
        public ProcessCompositionPage()
        {
            // 初始化XAML定义的组件
            InitializeComponent();
            // 设置数据上下文为工艺路线组成视图模型
            this.DataContext = new ProcessCompositionViewModel();
        }

        private void DataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {

        }
    }
}
