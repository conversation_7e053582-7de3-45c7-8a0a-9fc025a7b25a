using System.Windows;
using System.Windows.Controls;
using System.Threading;

namespace WpfApp.Plan
{
    /// <summary>
    /// ProductSelectionWindow.xaml 的交互逻辑
    /// </summary>
    public partial class ProductSelectionWindow : Window
    {
        public ProductSelectionViewModel ViewModel { get; private set; }
        public ProductDisplayModel SelectedProduct { get; private set; }
        private Timer _searchTimer;

        public ProductSelectionWindow()
        {
            InitializeComponent();
            ViewModel = new ProductSelectionViewModel();
            ViewModel.CloseWindow = (dialogResult, productModel) =>
            {
                if (dialogResult)
                {
                    SelectedProduct = productModel;
                    System.Diagnostics.Debug.WriteLine($"窗口关闭时设置选中产品: {productModel?.ProductName} - {productModel?.ProductCode}");
                }
                DialogResult = dialogResult;
                Close();
            };
            DataContext = ViewModel;
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // 取消之前的搜索定时器
            _searchTimer?.Dispose();

            // 设置新的搜索定时器，延迟800ms执行搜索
            _searchTimer = new Timer(state =>
            {
                Dispatcher.Invoke(() =>
                {
                    if (ViewModel != null)
                    {
                        ViewModel.SearchCommand.Execute(null);
                    }
                });
            }, null, 800, Timeout.Infinite);
        }

        protected override void OnClosed(System.EventArgs e)
        {
            _searchTimer?.Dispose();
            ViewModel?.Dispose();
            base.OnClosed(e);
        }
    }
}
