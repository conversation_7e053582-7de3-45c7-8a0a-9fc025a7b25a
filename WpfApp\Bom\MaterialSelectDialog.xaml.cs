using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;

namespace WpfApp.Bom
{
    public partial class MaterialSelectDialog : Window
    {
        public MaterialSelectViewModel ViewModel { get; private set; }
        public List<MaterialSelectItem> SelectedMaterials { get; private set; }

        public MaterialSelectDialog()
        {
            InitializeComponent();
            ViewModel = new MaterialSelectViewModel();
            DataContext = ViewModel;
            SelectedMaterials = new List<MaterialSelectItem>();
        }

        private void Confirm_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("确定按钮被点击");
                System.Diagnostics.Debug.WriteLine($"ViewModel.Materials总数: {ViewModel.Materials?.Count ?? 0}");

                // 获取选中的物料
                SelectedMaterials = ViewModel.Materials?.Where(m => m.IsSelected).ToList() ?? new List<MaterialSelectItem>();

                System.Diagnostics.Debug.WriteLine($"选中的物料数量: {SelectedMaterials.Count}");

                foreach (var material in SelectedMaterials)
                {
                    System.Diagnostics.Debug.WriteLine($"选中物料: {material.MaterialCode} - {material.MaterialName}");
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Confirm_Click异常: {ex.Message}");
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    // 选项卡背景颜色转换器
    public class BooleanToTabBackgroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isSelected && isSelected)
            {
                return new SolidColorBrush(Color.FromRgb(25, 118, 210)); // #1976D2
            }
            return new SolidColorBrush(Color.FromRgb(224, 224, 224)); // #E0E0E0
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    // 选项卡文字颜色转换器
    public class BooleanToTabTextColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isSelected && isSelected)
            {
                return new SolidColorBrush(Colors.White);
            }
            return new SolidColorBrush(Color.FromRgb(153, 153, 153)); // #999
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
