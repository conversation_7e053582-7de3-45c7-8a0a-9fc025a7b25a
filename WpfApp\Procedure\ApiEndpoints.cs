namespace WpfApp.Procedure
{
    /// <summary>
    /// API端点管理静态类
    /// 用于集中管理所有API URL，避免硬编码
    /// </summary>
    public static class ApiEndpoints
    {
        /// <summary>
        /// API服务器基础URL
        /// </summary>
        private const string BaseUrl = "http://localhost:5005";
        
        /// <summary>
        /// 工艺路线相关API
        /// </summary>
        public static class ProcessRoute
        {
            /// <summary>
            /// 获取工序列表API
            /// </summary>
            public static string ProcessStepList => $"{BaseUrl}/api/processRoute/processStepList";
            
            /// <summary>
            /// 添加工序API
            /// </summary>
            public static string AddProcessStep => $"{BaseUrl}/api/processRoute/addProcessStep";
            
            /// <summary>
            /// 更新工序API
            /// </summary>
            public static string UpdateProcessStep => $"{BaseUrl}/api/processRoute/updateProcessStep";
            
            /// <summary>
            /// 删除工序API
            /// </summary>
            public static string DeleteProcessStep => $"{BaseUrl}/api/processRoute/deleteProcessStep";
        }
    }
} 