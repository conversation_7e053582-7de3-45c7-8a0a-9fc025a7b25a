<Window x:Class="WpfApp.ProcessRoute.ProductMultiSelectDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="选择产品" Height="600" Width="900"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        MinHeight="500" MinWidth="800"
        Background="#F5F5F5">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="Package" 
                                     Foreground="#2196F3" 
                                     VerticalAlignment="Center" 
                                     Width="24" Height="24"
                                     Margin="0,0,10,0"/>
            <TextBlock Text="选择产品" FontSize="20" FontWeight="Bold" 
                       Foreground="#2196F3" VerticalAlignment="Center"/>
        </StackPanel>

        <!-- 搜索和操作区域 -->
        <Grid Grid.Row="1" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 搜索框 -->
            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <TextBox x:Name="SearchTextBox"
                         Text="{Binding SearchKeyword, UpdateSourceTrigger=PropertyChanged}"
                         Width="300" Height="40" Margin="0,0,10,0"
                         VerticalContentAlignment="Center"
                         Padding="10,0"/>
                <Button Content="搜索" Command="{Binding SearchCommand}"
                        Background="#2196F3" Foreground="White"
                        Width="80" Height="40" Margin="0,0,10,0"
                        BorderThickness="0" Cursor="Hand"/>
            </StackPanel>

            <!-- 批量操作按钮 -->
            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button Content="全选" Command="{Binding SelectAllCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Width="60" Height="40" Margin="0,0,5,0"/>
                <Button Content="反选" Command="{Binding UnselectAllCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Width="60" Height="40"/>
            </StackPanel>
        </Grid>

        <!-- 产品列表 -->
        <Border Grid.Row="2" Background="White" CornerRadius="5" 
                BorderBrush="#E0E0E0" BorderThickness="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 表头 -->
                <Border Grid.Row="0" Background="#F8F9FA" 
                        BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
                    <Grid Height="45">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="50"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="80"/>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="100"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="选择" HorizontalAlignment="Center" 
                                   VerticalAlignment="Center" FontWeight="Medium"/>
                        <TextBlock Grid.Column="1" Text="产品编号" HorizontalAlignment="Center" 
                                   VerticalAlignment="Center" FontWeight="Medium"/>
                        <TextBlock Grid.Column="2" Text="产品名称" HorizontalAlignment="Center" 
                                   VerticalAlignment="Center" FontWeight="Medium"/>
                        <TextBlock Grid.Column="3" Text="规格型号" HorizontalAlignment="Center" 
                                   VerticalAlignment="Center" FontWeight="Medium"/>
                        <TextBlock Grid.Column="4" Text="单位" HorizontalAlignment="Center" 
                                   VerticalAlignment="Center" FontWeight="Medium"/>
                        <TextBlock Grid.Column="5" Text="产品类型" HorizontalAlignment="Center" 
                                   VerticalAlignment="Center" FontWeight="Medium"/>
                        <TextBlock Grid.Column="6" Text="产品属性" HorizontalAlignment="Center" 
                                   VerticalAlignment="Center" FontWeight="Medium"/>
                    </Grid>
                </Border>

                <!-- 数据列表 -->
                <DataGrid Grid.Row="1" x:Name="ProductDataGrid"
                          ItemsSource="{Binding Products}"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          CanUserReorderColumns="False"
                          CanUserResizeRows="False"
                          HeadersVisibility="None"
                          GridLinesVisibility="Horizontal"
                          HorizontalGridLinesBrush="#F0F0F0"
                          VerticalGridLinesBrush="Transparent"
                          Background="White"
                          BorderThickness="0"
                          SelectionMode="Extended"
                          SelectionUnit="FullRow"
                          RowHeight="40">

                    <DataGrid.Columns>
                        <!-- 多选框列 -->
                        <DataGridTemplateColumn Width="50" IsReadOnly="False">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <CheckBox HorizontalAlignment="Center" VerticalAlignment="Center"
                                              IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- 产品编号 -->
                        <DataGridTextColumn Width="120" Binding="{Binding ProductCode}" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="5"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 产品名称 -->
                        <DataGridTextColumn Width="*" Binding="{Binding ProductName}" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Left"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="10,5"/>
                                    <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 规格型号 -->
                        <DataGridTextColumn Width="120" Binding="{Binding Specification}" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="5"/>
                                    <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 单位 -->
                        <DataGridTextColumn Width="80" Binding="{Binding Unit}" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="5"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 产品类型 -->
                        <DataGridTextColumn Width="100" Binding="{Binding ProductType}" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="5"/>
                                    <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 产品属性 -->
                        <DataGridTextColumn Width="100" Binding="{Binding ProductAttribute}" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="5"/>
                                    <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>

                    <!-- 行样式 -->
                    <DataGrid.RowStyle>
                        <Style TargetType="DataGridRow">
                            <Setter Property="Background" Value="White"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#F5F5F5"/>
                                </Trigger>
                                <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                    <Setter Property="Background" Value="#E3F2FD"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </DataGrid.RowStyle>
                </DataGrid>

                <!-- 加载指示器 -->
                <Grid Grid.Row="1" Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <Grid.Background>
                        <SolidColorBrush Color="White" Opacity="0.8"/>
                    </Grid.Background>
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <ProgressBar IsIndeterminate="True" Width="50" Height="50" 
                                     Style="{StaticResource MaterialDesignCircularProgressBar}"/>
                        <TextBlock Text="加载中..." Margin="0,10,0,0" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>

        <!-- 分页信息 -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
            <Button Content="上一页" Command="{Binding PreviousPageCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Width="80" Height="35" Margin="0,0,10,0"/>
            <TextBlock Text="{Binding PageInfo}" VerticalAlignment="Center" 
                       Margin="10,0" FontSize="14" Foreground="#666"/>
            <Button Content="下一页" Command="{Binding NextPageCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Width="80" Height="35" Margin="10,0,0,0"/>
        </StackPanel>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,15,0,0">
            <Button Content="取消" Command="{Binding CancelCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}" 
                    Width="80" Height="40" Margin="0,0,10,0"/>
            <Button Content="确定" Command="{Binding ConfirmCommand}"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Background="#2196F3" Foreground="White" 
                    Width="80" Height="40"/>
        </StackPanel>
    </Grid>
</Window>
