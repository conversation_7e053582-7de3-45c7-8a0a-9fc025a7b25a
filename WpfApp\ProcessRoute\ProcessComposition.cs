﻿// 引用系统基础类库
using System;
// 引用集合类库
using System.Collections.Generic;
// 引用LINQ查询类库
using System.Linq;
// 引用文本处理类库
using System.Text;
// 引用异步任务类库
using System.Threading.Tasks;
// 引用JSON序列化注解类库
using System.Text.Json.Serialization;

// 定义工艺路线命名空间
namespace WpfApp.ProcessRoute
{
    /// <summary>
    /// 工艺路线组成数据模型类
    /// 用于存储和传输工艺路线组成的相关信息
    /// </summary>
    public class ProcessComposition
    {
        /// <summary>
        /// 序列号 - 使用灵活字符串转换器处理可能的数字或字符串类型
        /// </summary>
        [JsonConverter(typeof(FlexibleStringConverter))]
        public string SerialCode { get; set; }

        /// <summary>
        /// 工艺脚本ID - 使用灵活整数转换器处理可能的字符串或数字类型
        /// </summary>
        [JsonConverter(typeof(FlexibleIntConverter))]
        public int ProcessScriptId { get; set; }

        /// <summary>
        /// 下一工序 - 使用灵活字符串转换器处理可能的数字或字符串类型
        /// </summary>
        [JsonConverter(typeof(FlexibleStringConverter))]
        public string NextProcedure { get; set; }

        /// <summary>
        /// 关系 - 使用灵活字符串转换器处理可能的数字或字符串类型
        /// </summary>
        [JsonConverter(typeof(FlexibleStringConverter))]
        public string Relationship { get; set; }

        /// <summary>
        /// 是否为关键工序 - 布尔值类型
        /// </summary>
        public bool IsKeyProcess { get; set; }

        /// <summary>
        /// 显示图标 - 使用灵活字符串转换器处理可能的数字或字符串类型
        /// </summary>
        [JsonConverter(typeof(FlexibleStringConverter))]
        public string DisplayIcon { get; set; }

        /// <summary>
        /// 准备时间 - 使用灵活字符串转换器处理可能的数字或字符串类型
        /// </summary>
        [JsonConverter(typeof(FlexibleStringConverter))]
        public string PreparationTime { get; set; }

        /// <summary>
        /// 等待时间 - 使用灵活整数转换器处理可能的字符串或数字类型
        /// </summary>
        [JsonConverter(typeof(FlexibleIntConverter))]
        public int WaitingTime { get; set; }

        /// <summary>
        /// 备注信息 - 使用灵活字符串转换器处理可能的数字或字符串类型
        /// </summary>
        [JsonConverter(typeof(FlexibleStringConverter))]
        public string Remarks { get; set; }

        /// <summary>
        /// 工艺路线ID - 使用灵活字符串转换器处理可能的数字或字符串类型
        /// </summary>
        [JsonConverter(typeof(FlexibleStringConverter))]
        public string ProcessRouteId { get; set; }

        /// <summary>
        /// 是否已删除 - 布尔值类型
        /// </summary>
        public bool IsDelete { get; set; }

        /// <summary>
        /// 创建时间 - 日期时间类型
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建者用户ID - 使用灵活字符串转换器处理可能的数字或字符串类型
        /// </summary>
        [JsonConverter(typeof(FlexibleStringConverter))]
        public string CreatorUserId { get; set; }

        /// <summary>
        /// 创建者用户名 - 使用灵活字符串转换器处理可能的数字或字符串类型
        /// </summary>
        [JsonConverter(typeof(FlexibleStringConverter))]
        public string CreatorUserName { get; set; }

        /// <summary>
        /// 更新者用户名 - 使用灵活字符串转换器处理可能的数字或字符串类型
        /// </summary>
        [JsonConverter(typeof(FlexibleStringConverter))]
        public string UpdateUserName { get; set; }

        /// <summary>
        /// 记录ID - 使用灵活字符串转换器处理可能的数字或字符串类型
        /// </summary>
        [JsonConverter(typeof(FlexibleStringConverter))]
        public string Id { get; set; }

        /// <summary>
        /// 创建时间显示格式 - 只读属性，格式化为yyyy-MM-dd HH:mm:ss
        /// </summary>
        public string CreateTimeDisplay => CreateTime.ToString("yyyy-MM-dd HH:mm:ss");

        /// <summary>
        /// 关键工序显示文本 - 只读属性，将布尔值转换为中文显示
        /// </summary>
        public string IsKeyProcessDisplay => IsKeyProcess ? "是" : "否";

        /// <summary>
        /// 状态显示文本 - 只读属性，根据删除标志显示状态
        /// </summary>
        public string StatusDisplay => IsDelete ? "已删除" : "启用";
    }
    /// <summary>
    /// API返回结果的通用泛型类
    /// 用于封装所有API接口的返回数据格式
    /// </summary>
    /// <typeparam name="T">返回数据的具体类型</typeparam>
    public class ApiResult<T>
    {
        /// <summary>
        /// 返回状态码 - 表示API调用是否成功
        /// </summary>
        public int Code { get; set; }

        /// <summary>
        /// 返回类型 - 描述返回结果的类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 返回消息 - 包含成功或错误的详细信息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 返回的具体数据 - 泛型类型，可以是任何数据类型
        /// </summary>
        public T Result { get; set; }
    }

    /// <summary>
    /// 工艺路线组成简化数据模型类
    /// 用于处理API返回的显示格式数据
    /// </summary>
    public class ProcessCompositionSimple
    {
        /// <summary>
        /// 序列号显示
        /// </summary>
        public string SerialCodeDisplay { get; set; }

        /// <summary>
        /// 工艺脚本ID显示
        /// </summary>
        public string ProcessScriptIdDisplay { get; set; }

        /// <summary>
        /// 下一工序显示
        /// </summary>
        public string NextProcedureDisplay { get; set; }

        /// <summary>
        /// 关系显示
        /// </summary>
        public string RelationshipDisplay { get; set; }

        /// <summary>
        /// 是否关键工序显示
        /// </summary>
        public string IsKeyProcessDisplay { get; set; }

        /// <summary>
        /// 显示图标显示
        /// </summary>
        public string DisplayIconDisplay { get; set; }

        /// <summary>
        /// 准备时间显示
        /// </summary>
        public string PreparationTimeDisplay { get; set; }

        /// <summary>
        /// 等待时间显示
        /// </summary>
        public string WaitingTimeDisplay { get; set; }

        /// <summary>
        /// 备注显示
        /// </summary>
        public string RemarksDisplay { get; set; }

        /// <summary>
        /// 工艺路线ID显示
        /// </summary>
        public string ProcessRouteIdDisplay { get; set; }

        /// <summary>
        /// 状态显示
        /// </summary>
        public string StatusDisplay { get; set; }

        /// <summary>
        /// 创建时间显示
        /// </summary>
        public string CreateTimeDisplay { get; set; }

        /// <summary>
        /// 创建者用户ID显示
        /// </summary>
        public string CreatorUserIdDisplay { get; set; }

        /// <summary>
        /// 创建者用户名显示
        /// </summary>
        public string CreatorUserNameDisplay { get; set; }

        /// <summary>
        /// 更新者用户名显示
        /// </summary>
        public string UpdateUserNameDisplay { get; set; }

        /// <summary>
        /// 记录ID显示
        /// </summary>
        public string IdDisplay { get; set; }
    }
}
