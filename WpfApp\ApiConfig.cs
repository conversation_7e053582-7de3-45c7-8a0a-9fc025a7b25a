namespace WpfApp
{
    public static class ApiConfig
    {
        // 后端API基础地址
        public static string BaseUrl = "http://8.140.238.209:8085";
        
        // API端点
        public static class Endpoints
        {
            // 认证相关
            public static string Login = "/api/sysAuth/simpleLogin";
            
            // 生产计划相关
            public static string ProductionPlan = "/api/productionPlan/productionPlan";
            public static string ProductionPlanDetail = "/api/productionPlan/productionPlanDetail";
            public static string ProductionPlanPage = "/api/productionPlan/page";
            public static string SourceTypeList = "/api/productionPlan/sourceTypeList";
            public static string DecomposeProductionPlan = "/api/productionPlan/decomposeProductionPlan";
            public static string WithdrawProductionPlan = "/api/productionPlan/withdrawProductionPlan";
            public static string BatchDeleteProductionPlan = "/api/productionPlan/batchDeleteProductionPlan";
            public static string UploadAttachment = "/api/productionPlanUpload/uploadAttachment";
            
            // BOM相关
            public static string Bom = "/api/bom/bom";
            public static string PageBomEntity = "/api/bom/pageBomEntity";
            public static string PageProduct = "/api/bom/pageProduct";
            public static string PageProcessRoute = "/api/bom/pageProcessRoute";
            public static string GetProductByCode = "/api/bom/getProductByCode";
            public static string GetProcessRouteByCode = "/api/bom/getProcessRouteByCode";
            public static string PageProcessComposition = "/api/bom/pageProcessComposition";
            
            // 工单相关
            public static string WorkOrder = "/api/workOrder/workOrder";
            public static string WorkOrderDetail = "/api/workOrder/detail";
            public static string MaterialEntityList = "/api/workOrder/materialEntityList";
            public static string ProductSchedu = "/api/workOrder/productSchedu";
            
            // 任务相关
            public static string Task = "/api/task/task";
            public static string DelTask = "/api/task/delTask";
            public static string UpdTask = "/api/task/updTask";
            public static string Sites = "/api/task/sites";
            public static string TaskSitesProcessRoute = "/api/task/taskSitesProcessRoute";
        }
        
        // 获取完整的API URL
        public static string GetUrl(string endpoint)
        {
            return $"{BaseUrl}{endpoint}";
        }
    }
} 