<Window x:Class="WpfApp.Plan.BomSelectionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:WpfApp.Plan"
        xmlns:app="clr-namespace:WpfApp"
        mc:Ignorable="d"
        Title="选择BOM" Height="500" Width="700" 
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="#F5F5F5">
    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <BooleanToVisibilityConverter x:Key="BoolToYesNoConverter"/>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <TextBlock Grid.Row="0" Text="选择BOM" FontSize="16" FontWeight="Bold" 
                 Margin="20,15,0,15" HorizontalAlignment="Left" VerticalAlignment="Center"/>
        
        <!-- 产品信息区域 -->
        <Border Grid.Row="1" Background="White" Margin="20,0,20,15" Padding="15,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="产品名称:" Foreground="#333333" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <TextBlock Grid.Column="1" Text="{Binding ProductName}" Foreground="#333333" VerticalAlignment="Center" Margin="0,0,15,0"/>
                
                <TextBlock Grid.Column="2" Text="产品编号:" Foreground="#333333" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <TextBlock Grid.Column="3" Text="{Binding ProductCode}" Foreground="#333333" VerticalAlignment="Center" Margin="0,0,15,0"/>
                
                <TextBlock Grid.Column="4" Text="规格型号:" Foreground="#333333" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <TextBlock Grid.Column="5" Text="{Binding Specification}" Foreground="#333333" VerticalAlignment="Center" Margin="0,0,15,0"/>
                
                <TextBlock Grid.Column="6" Text="单位:" Foreground="#333333" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <TextBlock Grid.Column="7" Text="{Binding Unit}" Foreground="#333333" VerticalAlignment="Center"/>
            </Grid>
        </Border>
        
        <!-- 搜索框 -->
        <Grid Grid.Row="2" Margin="20,0,20,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBox Grid.Column="0" Text="{Binding SearchKeyword, UpdateSourceTrigger=PropertyChanged}" 
                     Padding="10,8" materialDesign:HintAssist.Hint="请输入BOM编号搜索"
                     BorderThickness="1" BorderBrush="#DDDDDD"/>
            
            <Button Grid.Column="1" Margin="10,0,0,0" Content="搜索" Command="{Binding SearchCommand}"
                    Background="#3080FE" Foreground="White" Width="70" Height="30">
                <Button.Resources>
                    <Style TargetType="Border">
                        <Setter Property="CornerRadius" Value="2"/>
                    </Style>
                </Button.Resources>
            </Button>
        </Grid>
        
        <!-- BOM列表 -->
        <Border Grid.Row="3" Background="White" Margin="20,0,20,15">
            <Grid>
                <DataGrid ItemsSource="{Binding Boms}" AutoGenerateColumns="False" 
                          CanUserAddRows="False" CanUserDeleteRows="False"
                          CanUserResizeRows="False" IsReadOnly="True"
                          SelectionMode="Single" SelectedItem="{Binding SelectedBom}"
                          HeadersVisibility="Column"
                          BorderThickness="0" GridLinesVisibility="Horizontal"
                          Background="White" RowBackground="White" AlternatingRowBackground="#F9F9F9">
                    <DataGrid.Resources>
                        <Style TargetType="{x:Type DataGridColumnHeader}">
                            <Setter Property="Background" Value="White"/>
                            <Setter Property="Padding" Value="10,8"/>
                            <Setter Property="BorderBrush" Value="#E8E8E8"/>
                            <Setter Property="BorderThickness" Value="0,0,0,1"/>
                            <Setter Property="FontWeight" Value="Normal"/>
                        </Style>
                        <Style TargetType="{x:Type DataGridRow}">
                            <Setter Property="BorderBrush" Value="#E8E8E8"/>
                            <Setter Property="BorderThickness" Value="0,0,0,1"/>
                        </Style>
                    </DataGrid.Resources>
                    <DataGrid.Columns>
                        <DataGridTemplateColumn Width="50">
                            <DataGridTemplateColumn.Header>
                                <TextBlock Text="" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            </DataGridTemplateColumn.Header>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <RadioButton GroupName="BomSelection" IsChecked="{Binding RelativeSource={RelativeSource AncestorType=DataGridRow}, Path=IsSelected}" 
                                                 HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTextColumn Width="Auto" Header="序号" Binding="{Binding RowNumber}"/>
                        <DataGridTextColumn Width="*" Header="BOM编号" Binding="{Binding BomCode}"/>
                        <DataGridTextColumn Width="Auto" Header="版本号" Binding="{Binding BomVersion}"/>
                        <DataGridTemplateColumn Width="Auto" Header="默认BOM">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding IsDefaultBom, Converter={StaticResource BoolToYesNoConverter}}" 
                                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTextColumn Width="Auto" Header="日产量" Binding="{Binding DailyOutput}"/>
                    </DataGrid.Columns>
                </DataGrid>
                
                <!-- 加载状态 -->
                <Grid Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <Rectangle Fill="#88FFFFFF"/>
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}" 
                                    IsIndeterminate="True" Value="0" Width="40" Height="40"/>
                        <TextBlock Text="加载中..." Margin="0,10,0,0" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
                
                <!-- 暂无数据 -->
                <Grid Visibility="{Binding IsEmpty, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="FileDocumentOutline" Width="60" Height="60" HorizontalAlignment="Center" Foreground="#DDDDDD"/>
                        <TextBlock Text="暂无数据" Foreground="#999999" Margin="0,10,0,0" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>
        
        <!-- 分页控件 -->
        <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
            <TextBlock Text="{Binding TotalCountText}" Foreground="#666666" VerticalAlignment="Center" Margin="0,0,20,0"/>
            <Button Content="&lt;" Style="{StaticResource MaterialDesignOutlinedButton}" 
                    Height="28" Width="28" Padding="0" Margin="0,0,5,0"
                    Command="{Binding PrevPageCommand}"/>
            <TextBox Text="{Binding CurrentPage, UpdateSourceTrigger=PropertyChanged}" Width="40" 
                     TextAlignment="Center" Padding="5" Margin="0,0,5,0"/>
            <Button Content="&gt;" Style="{StaticResource MaterialDesignOutlinedButton}" 
                    Height="28" Width="28" Padding="0" Margin="0,0,20,0"
                    Command="{Binding NextPageCommand}"/>
            <TextBlock Text="前往" Foreground="#666666" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <TextBox Width="40" TextAlignment="Center" Padding="5" Margin="0,0,5,0"
                     Text="{Binding GoToPage, UpdateSourceTrigger=LostFocus}"/>
            <TextBlock Text="页" Foreground="#666666" VerticalAlignment="Center" Margin="0,0,5,0"/>
        </StackPanel>
        
        <!-- 按钮栏 -->
        <StackPanel Grid.Row="5" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,20,20">
            <Button Content="取消" Command="{Binding CancelCommand}" 
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Width="70" Height="30" Margin="0,0,10,0"/>
            <Button Content="确定" Command="{Binding ConfirmCommand}"
                    Background="#3080FE" Foreground="White" Width="70" Height="30">
                <Button.Resources>
                    <Style TargetType="Border">
                        <Setter Property="CornerRadius" Value="2"/>
                    </Style>
                </Button.Resources>
            </Button>
        </StackPanel>
    </Grid>
</Window> 