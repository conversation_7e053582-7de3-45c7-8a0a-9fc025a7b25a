<!-- 工序管理页面用户控件定义 -->
<UserControl x:Class="WpfApp.Procedure.ProcedurePage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">
    <!-- 主网格容器，设置外边距为10像素 -->
    <Grid Margin="10">
        <!-- 定义网格行布局 -->
        <Grid.RowDefinitions>
            <!-- 第一行：标题栏，自动高度 -->
            <RowDefinition Height="Auto"/>
            <!-- 第二行：操作按钮区域，自动高度 -->
            <RowDefinition Height="Auto"/>
            <!-- 第三行：数据表格区域，占用剩余空间 -->
            <RowDefinition Height="*"/>
            <!-- 第四行：分页控件区域，自动高度 -->
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏区域 -->
        <TextBlock Grid.Row="0" Text="工序管理" FontSize="24" FontWeight="Bold" 
                   Margin="0,0,0,20" HorizontalAlignment="Center"/>
        
        <!-- 操作按钮区域 -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,10" Padding="15">
            <!-- 水平排列的操作按钮容器 -->
            <StackPanel Orientation="Horizontal">
                <!-- 新增按钮 -->
                <Button Content="新增" Command="{Binding AddCommand}" 
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Background="#4CAF50" Foreground="White" Margin="0,0,10,0"/>
                <!-- 删除按钮 -->
                <Button Content="删除" Command="{Binding DeleteSelectedCommand}"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Background="#F44336" Foreground="White" Margin="0,0,10,0"/>
                <!-- 导出按钮 -->
                <Button Content="导出" Command="{Binding ExportCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}" Margin="0,0,10,0"/>
                <!-- 刷新按钮 -->
                <Button Content="刷新" Command="{Binding RefreshCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"/>
            </StackPanel>
        </materialDesign:Card>
        
        <!-- 数据表格区域 -->
        <materialDesign:Card Grid.Row="2" Padding="0">
            <!-- 数据表格控件 -->
            <DataGrid ItemsSource="{Binding ProcessSteps}" 
                      SelectedItem="{Binding SelectedProcessStep}"
                      AutoGenerateColumns="False" 
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      SelectionMode="Single"
                      AlternatingRowBackground="#F5F5F5">
                <!-- 定义数据表格列 -->
                <DataGrid.Columns>
                    <!-- 选择框列 -->
                    <DataGridCheckBoxColumn Width="40" Binding="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}">
                        <DataGridCheckBoxColumn.Header>
                            <CheckBox x:Name="selectAllCheckBox"
                                      IsChecked="{Binding IsAllSelected, UpdateSourceTrigger=PropertyChanged}"
                                      Checked="SelectAllCheckBox_Checked"
                                      Unchecked="SelectAllCheckBox_Unchecked"/>
                        </DataGridCheckBoxColumn.Header>
                    </DataGridCheckBoxColumn>
                    <!-- 序号列 -->
                    <DataGridTextColumn Header="序号" Width="60" Binding="{Binding RowNumber}"/>
                    <!-- 工序名称列 -->
                    <DataGridTextColumn Header="工序名称" Width="150" Binding="{Binding ProcessName}"/>
                    <!-- 工序编号列 -->
                    <DataGridTextColumn Header="工序编号" Width="120" Binding="{Binding ProcessCode}"/>
                    <!-- 状态列 -->
                    <DataGridTextColumn Header="状态" Width="80" Binding="{Binding StatusDisplay}"/>
                    <!-- 工序说明列 -->
                    <DataGridTextColumn Header="工序说明" Width="200" Binding="{Binding Description}"/>
                    <!-- 备注列 -->
                    <DataGridTextColumn Header="备注" Width="150" Binding="{Binding Remark}"/>
                    <!-- 操作列 - 使用模板列定义操作按钮 -->
                    <DataGridTemplateColumn Header="操作" Width="120">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <!-- 水平排列的操作按钮容器 -->
                                <StackPanel Orientation="Horizontal">
                                    <!-- 编辑按钮 -->
                                    <Button Content="编辑" 
                                            Command="{Binding DataContext.EditCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource MaterialDesignFlatButton}"
                                            Foreground="#2196F3" FontSize="12" Padding="5,2"/>
                                    <!-- 删除按钮 -->
                                    <Button Content="删除" 
                                            Command="{Binding DataContext.DeleteCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource MaterialDesignFlatButton}"
                                            Foreground="#F44336" FontSize="12" Padding="5,2"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
        
        <!-- 分页控件区域 -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" 
                    VerticalAlignment="Center" Margin="0,10,0,0">
            <!-- 上一页按钮 -->
            <Button Content="上一页" Command="{Binding PrevPageCommand}" 
                    Style="{StaticResource MaterialDesignOutlinedButton}" Margin="5"/>
            <!-- 当前页信息显示 -->
            <TextBlock Text="{Binding CurrentPageDisplay}" VerticalAlignment="Center" Margin="10,0"/>
            <!-- 下一页按钮 -->
            <Button Content="下一页" Command="{Binding NextPageCommand}" 
                    Style="{StaticResource MaterialDesignOutlinedButton}" Margin="5"/>
            <!-- 总数标签 -->
            <TextBlock Text="总数:" VerticalAlignment="Center" Margin="20,0,5,0"/>
            <!-- 总数值显示 -->
            <TextBlock Text="{Binding TotalCount}" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <!-- 每页标签 -->
            <TextBlock Text="每页:" VerticalAlignment="Center" Margin="10,0,5,0"/>
            <!-- 每页数量选择下拉框 -->
            <ComboBox SelectedValue="{Binding PageSize}" Width="60" Margin="0,0,10,0">
                <!-- 每页10条选项 -->
                <ComboBoxItem Content="10"/>
                <!-- 每页20条选项 -->
                <ComboBoxItem Content="20"/>
                <!-- 每页50条选项 -->
                <ComboBoxItem Content="50"/>
                <!-- 每页100条选项 -->
                <ComboBoxItem Content="100"/>
            </ComboBox>
        </StackPanel>
    </Grid>
</UserControl> 