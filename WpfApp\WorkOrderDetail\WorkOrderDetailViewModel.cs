﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Linq;
using System.Net.Http;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Media.Media3D;
using WpfApp.ProductionSchedu;
using WpfApp.WorkOrder;

namespace WpfApp.WorkOrderDetail
{
    public class WorkOrderDetailViewModel : INotifyPropertyChanged
    {
        private long _workOrderId;
        private string _workOrderCode;
        private string _workOrderName;
        private string _planName;
        private string _planCode;
        private string _productName;
        private string _productCode;
        private string _specification;
        private string _unit;
        private string _productType;
        private string _bomCode;
        private string _bomVersion;
        private string _orderNumber;
        private int? _planNumber;
        private int? _realityNumber;
        private string _planStartTime;
        private string _planEndTime;
        private string _realityStartTime;
        private string _realityEndTime;
        private string _demandTime;
        private string _statusText;
        private string _remarks;
        private string _createTime;
        private string _createUser;
        private bool _isLoading;
        private WorkOrderModel _workOrder;
        public ICommand BackCommand;
        // 事件
        public event Action BackRequested;
        public WorkOrderDetailViewModel(WorkOrderModel workOrder)
        {
            _workOrderId = workOrder.WorkOrderId;
            BackCommand = new RelayCommand(_ => BackRequested?.Invoke());
            LoadWorkOrderDetailAsync();
        }
        // 属性
        public long WorkOrderId
        {
            get => _workOrderId;
            set { _workOrderId = value; OnPropertyChanged(); }
        }

        public string WorkOrderCode
        {
            get => _workOrderCode ?? "";
            set { _workOrderCode = value; OnPropertyChanged(); }
        }

        public string WorkOrderName
        {
            get => _workOrderName ?? "";
            set { _workOrderName = value; OnPropertyChanged(); }
        }

        public string PlanName
        {
            get => _planName ?? "";
            set { _planName = value; OnPropertyChanged(); }
        }

        public string PlanCode
        {
            get => _planCode ?? "";
            set { _planCode = value; OnPropertyChanged(); }
        }
        public string ProductName
        {
            get => _productName ?? "";
            set { _productName = value; OnPropertyChanged(); }
        }

        public string ProductCode
        {
            get => _productCode ?? "";
            set { _productCode = value; OnPropertyChanged(); }
        }

        public string Specification
        {
            get => _specification ?? "";
            set { _specification = value; OnPropertyChanged(); }
        }

        public string Unit
        {
            get => _unit ?? "";
            set { _unit = value; OnPropertyChanged(); }
        }

        public string ProductType
        {
            get => _productType ?? "";
            set { _productType = value; OnPropertyChanged(); }
        }

        public string BomCode
        {
            get => _bomCode ?? "";
            set { _bomCode = value; OnPropertyChanged(); }
        }

        public string BomVersion
        {
            get => _bomVersion ?? "";
            set { _bomVersion = value; OnPropertyChanged(); }
        }

        public string OrderNumber
        {
            get => _orderNumber ?? "";
            set { _orderNumber = value; OnPropertyChanged(); }
        }

        public int? PlanNumber
        {
            get => _planNumber;
            set { _planNumber = value; OnPropertyChanged(); }
        }

        public int? RealityNumber
        {
            get => _realityNumber;
            set { _realityNumber = value; OnPropertyChanged(); }
        }

        public string PlanStartTime
        {
            get => _planStartTime ?? "";
            set { _planStartTime = value; OnPropertyChanged(); }
        }
        public string PlanEndTime
        {
            get => _planEndTime ?? "";
            set { _planEndTime = value; OnPropertyChanged(); }
        }

        public string RealityStartTime
        {
            get => _realityStartTime ?? "";
            set { _realityStartTime = value; OnPropertyChanged(); }
        }

        public string RealityEndTime
        {
            get => _realityEndTime ?? "";
            set { _realityEndTime = value; OnPropertyChanged(); }
        }

        public string DemandTime
        {
            get => _demandTime ?? "";
            set { _demandTime = value; OnPropertyChanged(); }
        }

        public string StatusText
        {
            get => _statusText ?? "";
            set { _statusText = value; OnPropertyChanged(); }
        }

        public string Remarks
        {
            get => _remarks ?? "";
            set { _remarks = value; OnPropertyChanged(); }
        }

        public string CreateTime
        {
            get => _createTime ?? "";
            set { _createTime = value; OnPropertyChanged(); }
        }

        public string CreateUser
        {
            get => _createUser ?? "";
            set { _createUser = value; OnPropertyChanged(); }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                if(_isLoading != value)
                {
                    _isLoading = value;
                    OnPropertyChanged();
                    System.Diagnostics.Debug.WriteLine($"IsLoading: {value}");
                }
            }
        }
        //添加物料清单相关属性
        private ObservableCollection<MaterialModel> _materials = new ObservableCollection<MaterialModel>();
        public ObservableCollection<MaterialModel> Materials
        {
            get { return _materials; }
            set
            {
                _materials = value;
                OnPropertyChanged();
            }
        }

        private int _totalMaterialQuantity;
        public int TotalMaterialQuantity
        {
            get { return _totalMaterialQuantity; }
            set
            {
                _totalMaterialQuantity = value;
                OnPropertyChanged();
            }
        }
        //添加工序任务相关属性
        private ObservableCollection<ProcessStepModel> _processSteps = new ObservableCollection<ProcessStepModel>();
        public ObservableCollection<ProcessStepModel> ProcessSteps
        {
            get { return _processSteps; }
            set
            {
                _processSteps = value;
                OnPropertyChanged();
            }
        }

        private ObservableCollection<string> _processStepOptions = new ObservableCollection<string>();
        public ObservableCollection<string> ProcessStepOptions
        {
            get { return _processStepOptions; }
            set
            {
                _processStepOptions = value;
                OnPropertyChanged();
            }
        }
        private string _selectedProcessStepId = "1";
        public string SelectedProcessStepId
        {
            get => _selectedProcessStepId;
            set
            {
                if (_selectedProcessStepId != value)
                {
                    _selectedProcessStepId = value;
                    OnPropertyChanged();
                    // 当工序改变时重新加载步骤
                    if (!string.IsNullOrEmpty(ProcessRouteId) && !string.IsNullOrEmpty(value))
                    {
                        LoadProcessSteps(ProcessRouteId, value);
                    }
                }
            }
        }

        private string _processRouteId;
        public string ProcessRouteId
        {
            get => _processRouteId;
            set { _processRouteId = value; OnPropertyChanged(); }
        }

        private string _processRouteName;
        public string ProcessRouteName
        {
            get => _processRouteName;
            set { _processRouteName = value; OnPropertyChanged(); }
        }

        private string _processRouteCode;
        public string ProcessRouteCode
        {
            get => _processRouteCode;
            set { _processRouteCode = value; OnPropertyChanged(); }
        }

        private string _bomId;
        public string BomId
        {
            get => _bomId;
            set { _bomId = value; OnPropertyChanged(); }
        }

        //基础信息
        private async Task LoadWorkOrderDetailAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始加载工单详情，ID:{WorkOrderId}");
                IsLoading = true;

                using var client = new HttpClient();
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                var url = $"http://localhost:5005/api/workOrder/detail?workOrderId={WorkOrderId}";
                var json = await client.GetStringAsync(url);

                System.Diagnostics.Debug.WriteLine($"工单详情API返回: {json}");

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                var response = JsonSerializer.Deserialize<WorkOrderDetailResponse>(json, options);

                if (response?.Code == 200 && response.Result != null)
                {
                    var detail = response.Result;

                    // 反填数据
                    WorkOrderCode = detail.WorkOrderCode ?? "";
                    WorkOrderName = detail.WorkOrderName ?? "";
                    PlanName = detail.PlanName ?? "";
                    PlanCode = detail.PlanCode ?? "";
                    ProductName = detail.ProductName ?? "";
                    ProductCode = detail.ProductCode ?? "";
                    Specification = detail.Specification ?? "";
                    Unit = detail.Unit ?? "";
                    ProductType = detail.ProductType ?? "";
                    BomCode = detail.BomCode ?? "";
                    BomVersion = detail.BomVersion ?? "";
                    OrderNumber = detail.OrderNumber ?? "";
                    PlanNumber = detail.PlanNumber;
                    RealityNumber = detail.RealityNumber;
                    PlanStartTime = detail.PlanStartTime ?? "";
                    PlanEndTime = detail.PlanEndTime ?? "";
                    RealityStartTime = detail.RealityStartTime ?? "";
                    RealityEndTime = detail.RealityEndTime ?? "";
                    DemandTime = detail.DemandTime ?? "";
                    Remarks = detail.Remarks ?? "";
                    CreateTime = detail.CreateTime ?? "";
                    CreateUser = detail.CreateUser ?? "";
                    BomId = detail.BomId ?? "";
                    ProcessRouteCode = detail.ProcessRouteCode ?? "";
                    ProcessRouteName = detail.ProcessRouteName ?? "";
                    ProcessRouteId = detail.ProcessRouteId ?? "";

                    // 设置状态文本
                    StatusText = detail.Status switch
                    {
                        0 => "待排产",
                        1 => "未开始",
                        2 => "进行中",
                        3 => "已完成",
                        4 => "已撤回",
                        5 => "已取消",
                        _ => "未知状态"
                    };

                    System.Diagnostics.Debug.WriteLine($"工单详情加载成功: {WorkOrderName}");
                    System.Diagnostics.Debug.WriteLine($"BomId: {detail.BomId}");
                    System.Diagnostics.Debug.WriteLine($"ProcessRouteId: {detail.ProcessRouteId}");
                    System.Diagnostics.Debug.WriteLine($"ProcessSteps: {detail.ProcessSteps}");
                    if (!string.IsNullOrEmpty(detail.BomId))
                    {
                        await LoadMaterialListAsync(detail.BomId);
                    }
                    //加载工艺步骤
                    // 加载工艺步骤
                    if (!string.IsNullOrEmpty(detail.ProcessRouteId))
                    {
                        if (!string.IsNullOrEmpty(detail.ProcessSteps))
                        {
                            var stepIds = detail.ProcessSteps.Split(',', StringSplitOptions.RemoveEmptyEntries);
                            ProcessStepOptions.Clear();
                            foreach (var stepId in stepIds)
                            {
                                ProcessStepOptions.Add(stepId.Trim());
                            }

                            System.Diagnostics.Debug.WriteLine($"工序选项数量: {ProcessStepOptions.Count}");
                            foreach (var option in ProcessStepOptions)
                            {
                                System.Diagnostics.Debug.WriteLine($"工序选项: {option}");
                            }

                            if (ProcessStepOptions.Count > 0)
                            {
                                SelectedProcessStepId = ProcessStepOptions.First();
                                System.Diagnostics.Debug.WriteLine($"选择的工序ID: {SelectedProcessStepId}");
                                await LoadProcessStepsAsync(detail.ProcessRouteId, ProcessStepOptions.First());
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("ProcessSteps为空，尝试加载默认工序");
                            // 如果ProcessSteps为空，尝试加载默认工序
                            ProcessStepOptions.Clear();
                            ProcessStepOptions.Add("2");
                            ProcessStepOptions.Add("3");
                            SelectedProcessStepId = "2";
                            await LoadProcessStepsAsync(detail.ProcessRouteId, "2");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("ProcessRouteId为空");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"工单详情加载失败: {response?.Message}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载工单详情异常: {ex.Message}");
            }
            finally
            {
                System.Diagnostics.Debug.WriteLine("设置IsLoading=false");
                IsLoading = false;
            }
        }

        // 加载物料清单
        private async Task LoadMaterialListAsync(string bomId)
        {
            try
            {
                using var client = new HttpClient();
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                var url = $"http://localhost:5005/api/workOrder/materialEntityList?Page=1&PageSize=100&bomId={bomId}";
                var json = await client.GetStringAsync(url);

                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                var result = JsonSerializer.Deserialize<MaterialListResponse>(json, options);

                if (result?.Result?.Items != null)
                {
                    var materials = new List<MaterialModel>();
                    for (int i = 0; i < result.Result.Items.Count; i++)
                    {
                        var item = result.Result.Items[i];
                        materials.Add(new MaterialModel
                        {
                            Index = i + 1,
                            MaterialCode = item.MaterialCode,
                            MaterialName = item.MaterialName,
                            Specification = item.Specification,
                            Unit = item.Unit,
                            RequiredQuantity = item.UsageQuantity,
                            UsageRatio = item.UsageRatio
                        });
                    }
                    Materials = new ObservableCollection<MaterialModel>(materials);
                    TotalMaterialQuantity = (int)materials.Sum(m => m.RequiredQuantity);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载物料清单异常: {ex.Message}");
            }
        }

        // 加载工序任务
        // 修改LoadProcessStepsAsync方法
        private async Task LoadProcessStepsAsync(string processRouteId, string processStepId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始加载工序任务: ProcessRouteId={processRouteId}, ProcessStepId={processStepId}");

                using var client = new HttpClient();
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                var url = $"http://localhost:5005/api/task/taskSitesProcessRoute?ProcessRouteId={processRouteId}&ProcessStepId={processStepId}";
                System.Diagnostics.Debug.WriteLine($"请求URL: {url}");

                var json = await client.GetStringAsync(url);
                System.Diagnostics.Debug.WriteLine($"工序任务API返回: {json}");

                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                var result = JsonSerializer.Deserialize<ProcessStepResponse>(json, options);

                if (result?.Code == 200 && result.Result != null && result.Result.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"找到 {result.Result.Count} 个工序任务");

                    var processSteps = new List<ProcessStepModel>();
                    for (int i = 0; i < result.Result.Count; i++)
                    {
                        var item = result.Result[i];
                        System.Diagnostics.Debug.WriteLine($"任务 {i}: Id={item.Id}, TaskNumber={item.TaskNumber}, TaskName={item.TaskName}");

                        // 处理时间格式
                        string startTimeStr = "";
                        string endTimeStr = "";

                        if (!string.IsNullOrEmpty(item.StartTime))
                        {
                            if (DateTime.TryParse(item.StartTime, out var startTime))
                            {
                                startTimeStr = startTime.ToString("yyyy-MM-dd HH:mm");
                            }
                            else
                            {
                                startTimeStr = item.StartTime;
                            }
                        }

                        if (!string.IsNullOrEmpty(item.EndTime))
                        {
                            if (DateTime.TryParse(item.EndTime, out var endTime))
                            {
                                endTimeStr = endTime.ToString("yyyy-MM-dd HH:mm");
                            }
                            else
                            {
                                endTimeStr = item.EndTime;
                            }
                        }

                        processSteps.Add(new ProcessStepModel
                        {
                            Index = i + 1,
                            TaskId = item.Id ?? "",
                            TaskCode = item.TaskNumber ?? $"TASK{i + 1:D3}",
                            TaskName = item.TaskName ?? "未命名任务",
                            SiteName = item.SiteName ?? "",
                            SiteCode = item.SiteCode ?? "",
                            PlanQuantity = item.PlanQuantity ?? 0,
                            PlanStartTime = startTimeStr,
                            PlanEndTime = endTimeStr,
                            TaskColor = item.TaskColor ?? ""
                        });
                    }

                    ProcessSteps = new ObservableCollection<ProcessStepModel>(processSteps);
                    System.Diagnostics.Debug.WriteLine($"成功设置 {ProcessSteps.Count} 个工序任务到集合");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"没有找到工序任务数据，Code: {result?.Code}, Message: {result?.Message}");
                    ProcessSteps = new ObservableCollection<ProcessStepModel>();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载工序任务异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                ProcessSteps = new ObservableCollection<ProcessStepModel>();
            }
        }
        // 加载工序步骤（用于切换工序）
        private async void LoadProcessSteps(string processRouteId, string processStepId)
        {
            await LoadProcessStepsAsync(processRouteId, processStepId);
        }

        public event PropertyChangedEventHandler? PropertyChanged;//声明一个事件，在属性值发生变化时触发
        protected virtual void OnPropertyChanged([CallerMemberName]string propertyName=null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
