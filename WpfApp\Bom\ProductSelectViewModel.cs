using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.Json;
using WpfApp.Bom;

namespace WpfApp
{
    public class ProductSelectViewModel : INotifyPropertyChanged
    {
        private ObservableCollection<ProductItem> _products;
        private ProductItem _selectedProduct;

        public event Action<bool> CloseRequested;

        public ObservableCollection<ProductItem> Products
        {
            get => _products;
            set { _products = value; OnPropertyChanged(nameof(Products)); }
        }

        public ProductItem SelectedProduct
        {
            get => _selectedProduct;
            set { _selectedProduct = value; OnPropertyChanged(nameof(SelectedProduct)); }
        }

        public ICommand ConfirmCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand SelectProductCommand { get; }

        public ProductSelectViewModel()
        {
            ConfirmCommand = new WpfApp.Bom.RelayCommand(_ => Confirm());
            CancelCommand = new WpfApp.Bom.RelayCommand(_ => Cancel());
            SelectProductCommand = new WpfApp.Bom.RelayCommand(SelectProduct);

            LoadProductsAsync();
        }

        private async void LoadProductsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始加载产品数据...");

                using var httpClient = new HttpClient();

                // 从AuthContext获取token
                var token = AuthContext.Token;
                System.Diagnostics.Debug.WriteLine($"Token: {(string.IsNullOrEmpty(token) ? "空" : "已获取")}");

                if (!string.IsNullOrEmpty(token))
                {
                    httpClient.DefaultRequestHeaders.Authorization =
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
                }

                // 调用API获取产品列表
                var apiUrl = $"{ApiConfig.GetUrl(ApiConfig.Endpoints.PageProduct)}?Page=1&PageSize=50";
                System.Diagnostics.Debug.WriteLine($"调用API: {apiUrl}");

                var response = await httpClient.GetAsync(apiUrl);
                System.Diagnostics.Debug.WriteLine($"API响应状态: {response.StatusCode}");

                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    System.Diagnostics.Debug.WriteLine($"API响应内容: {jsonContent}");

                    var apiResponse = JsonSerializer.Deserialize<ApiResponse>(jsonContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        NumberHandling = System.Text.Json.Serialization.JsonNumberHandling.AllowReadingFromString
                    });

                    if (apiResponse?.Result?.Items != null && apiResponse.Code == 200)
                    {
                        System.Diagnostics.Debug.WriteLine($"获取到 {apiResponse.Result.Items.Count} 个产品");

                        var productItems = new ObservableCollection<ProductItem>();
                        for (int i = 0; i < apiResponse.Result.Items.Count; i++)
                        {
                            var product = apiResponse.Result.Items[i];
                            productItems.Add(new ProductItem
                            {
                                Index = i + 1,
                                Id = long.TryParse(product.Id, out var id) ? id : 0, // 转换字符串ID为long
                                ProductCode = product.ProductCode ?? "",
                                IsSystemCode = product.IsSystemCode,
                                ProductName = product.ProductName ?? "",
                                Specification = product.Specification ?? "",
                                Unit = product.Unit ?? "",
                                ProductType = product.ProductType ?? "",
                                ProductAttribute = product.ProductAttribute ?? ""
                            });
                        }
                        Products = productItems;
                        System.Diagnostics.Debug.WriteLine("产品数据加载成功");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"API响应码: {apiResponse?.Code}, 清空数据");
                        Products.Clear();
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"API调用失败: {response.StatusCode} - {response.ReasonPhrase}");
                    Products.Clear();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载产品数据异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常详情: {ex}");
                Products.Clear();
            }
        }



        private void Confirm()
        {
            // 检查是否有选中的产品
            if (SelectedProduct != null)
            {
                System.Diagnostics.Debug.WriteLine($"确认选择产品: {SelectedProduct.ProductName}");
                CloseRequested?.Invoke(true);
            }
            else
            {
                // 如果没有选中产品，可以显示提示信息
                System.Diagnostics.Debug.WriteLine("请先选择一个产品");
                // 这里可以添加消息框提示用户选择产品
                System.Windows.MessageBox.Show("请先选择一个产品", "提示",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
        }

        private void Cancel()
        {
            CloseRequested?.Invoke(false);
        }

        private void SelectProduct(object parameter)
        {
            if (parameter is ProductItem selectedProduct)
            {
                // 取消其他产品的选中状态
                foreach (var product in Products)
                {
                    if (product != selectedProduct)
                    {
                        product.IsSelected = false;
                    }
                }

                // 设置当前选中的产品
                SelectedProduct = selectedProduct;
                System.Diagnostics.Debug.WriteLine($"选中产品: {selectedProduct.ProductName}");
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string name) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }

    public class ProductItem : INotifyPropertyChanged
    {
        private bool _isSelected;

        public int Index { get; set; }

        /// <summary>
        /// 产品id
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 产品编号（唯一标识）
        /// </summary>
        public string ProductCode { get; set; }

        /// <summary>
        /// 是否设置系统编号（0/1）
        /// </summary>
        public bool IsSystemCode { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 产品规格型号
        /// </summary>
        public string Specification { get; set; }

        /// <summary>
        /// 产品计量单位（个/件/箱等）
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 产品类型（下拉选项值）
        /// </summary>
        public string ProductType { get; set; }

        /// <summary>
        /// 产品属性（下拉选项值）
        /// </summary>
        public string ProductAttribute { get; set; }

        /// <summary>
        /// 是否被选中
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // API响应模型 - 根据实际返回的JSON结构
    public class ApiResponse
    {
        public int Code { get; set; }
        public string Type { get; set; }
        public string Message { get; set; }
        public ProductPageResult Result { get; set; }
    }

    public class ProductPageResult
    {
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int Total { get; set; }
        public int TotalPages { get; set; }
        public List<ApiProductItem> Items { get; set; }
    }

    public class ApiProductItem
    {
        public string? Id { get; set; } // 根据JSON数据，ID是字符串类型
        public string? ProductCode { get; set; }
        public bool IsSystemCode { get; set; }
        public string? ProductName { get; set; }
        public string? Specification { get; set; }
        public string? Unit { get; set; }
        public string? ProductType { get; set; }
        public string? ProductAttribute { get; set; }
        public string? ProductCategory { get; set; }
        public bool IsEnabled { get; set; }
        public object ValidityDays { get; set; } // 使用object类型避免转换错误
        public object AlarmDays { get; set; } // 使用object类型避免转换错误
        public object StockUpperLimit { get; set; } // 使用object类型避免转换错误
        public object StockLowerLimit { get; set; } // 使用object类型避免转换错误
        public object PurchasePrice { get; set; } // 使用object类型避免转换错误
        public string? Remarks { get; set; }
        public string? ImagePath { get; set; }
        public string? BomID { get; set; }
        public bool IsDelete { get; set; }
        public string? CreateTime { get; set; }
        public string? UpdateTime { get; set; }
        public string? CreateUserId { get; set; }
        public string? CreateUserName { get; set; }
        public string? UpdateUserId { get; set; }
        public string? UpdateUserName { get; set; }
    }
}
