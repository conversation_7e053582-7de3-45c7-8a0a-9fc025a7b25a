﻿<UserControl x:Class="WpfApp.WorkTask.DispatchTaskPage"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WpfApp.WorkTask"
        mc:Ignorable="d"
        >
    <Grid Margin="30" Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="工单派工" FontSize="24" FontWeight="Bold" 
                   Margin="0,0,0,30" HorizontalAlignment="Center"/>

        <!-- 派工表单 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 任务派工 -->
                <GroupBox Header="任务派工" Margin="0,0,0,30" Padding="20">
                    <GroupBox.HeaderTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding}" FontSize="16" FontWeight="Bold" Foreground="#673AB7"/>
                        </DataTemplate>
                    </GroupBox.HeaderTemplate>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="20"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="20"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="班组名称*:" VerticalAlignment="Center" 
                   FontWeight="Medium" Margin="0,0,10,0"/>
                        <ComboBox Grid.Row="0" Grid.Column="1" ItemsSource="{Binding TeamOptions}" 
                  SelectedItem="{Binding SelectedTeam}" DisplayMemberPath="Name" 
                  Height="35" Margin="0,0,20,0"/>

                        <TextBlock Grid.Row="0" Grid.Column="2" Text="任务负责人*:" VerticalAlignment="Center" 
                   FontWeight="Medium" Margin="0,0,10,0"/>
                        <TextBox Grid.Row="0" Grid.Column="3" Text="{Binding TaskResponsible}" 
                 Height="35" Padding="8"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="其他成员:" VerticalAlignment="Center" 
                   FontWeight="Medium" Margin="0,0,10,0"/>
                        <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding OtherMembers}" 
                 Height="35" Margin="0,0,20,0" Padding="8"/>

                        <TextBlock Grid.Row="4" Grid.Column="0" Text="备注:" VerticalAlignment="Top" 
                   FontWeight="Medium" Margin="0,5,10,0"/>
                        <TextBox Grid.Row="4" Grid.Column="1" Grid.ColumnSpan="3" Text="{Binding TaskRemarks}" 
                 Height="80" TextWrapping="Wrap" AcceptsReturn="True" 
                 VerticalScrollBarVisibility="Auto" Padding="8"/>
                    </Grid>
                </GroupBox>

                <!-- 质检派工 -->
                <GroupBox Header="质检派工" Margin="0,0,0,20" Padding="20">
                    <GroupBox.HeaderTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding}" FontSize="16" FontWeight="Bold" Foreground="#673AB7"/>
                        </DataTemplate>
                    </GroupBox.HeaderTemplate>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="20"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="质检部门*:" VerticalAlignment="Center" 
                   FontWeight="Medium" Margin="0,0,10,0"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding QualityDept}" 
                 Height="35" Margin="0,0,20,0" Padding="8"/>

                        <TextBlock Grid.Row="0" Grid.Column="2" Text="质检人员*:" VerticalAlignment="Center" 
                   FontWeight="Medium" Margin="0,0,10,0"/>
                        <TextBox Grid.Row="0" Grid.Column="3" Text="{Binding QualityPerson}" 
                 Height="35" Padding="8"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="备注:" VerticalAlignment="Top" 
                   FontWeight="Medium" Margin="0,5,10,0"/>
                        <TextBox Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="3" Text="{Binding QualityRemarks}" 
                 Height="80" TextWrapping="Wrap" AcceptsReturn="True" 
                 VerticalScrollBarVisibility="Auto" Padding="8"/>
                    </Grid>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- 按钮 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,30,0,0">
            <Button Content="取消" Command="{Binding CancelCommand}" Width="100" Height="40" 
                    Margin="0,0,20,0" FontSize="14"/>
            <Button Content="确认派工" Command="{Binding ConfirmCommand}" Width="120" Height="40" 
                    Background="#673AB7" Foreground="White" FontSize="14"/>
        </StackPanel>
    </Grid>
</UserControl>
