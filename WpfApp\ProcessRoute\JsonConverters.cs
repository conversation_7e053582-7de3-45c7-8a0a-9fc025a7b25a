// 引用系统基础类库
using System;
// 引用JSON处理类库
using System.Text.Json;
// 引用JSON序列化注解类库
using System.Text.Json.Serialization;

// 定义工艺路线命名空间
namespace WpfApp.ProcessRoute
{
    /// <summary>
    /// 灵活的字符串转换器类
    /// 自定义JSON转换器，将数字、布尔值或字符串统一转换为字符串类型
    /// 用于处理API返回数据类型不一致的问题
    /// </summary>
    public class FlexibleStringConverter : JsonConverter<string>
    {
        /// <summary>
        /// 读取JSON数据并转换为字符串的方法
        /// </summary>
        /// <param name="reader">UTF8 JSON读取器</param>
        /// <param name="typeToConvert">要转换的目标类型</param>
        /// <param name="options">JSON序列化选项</param>
        /// <returns>转换后的字符串值</returns>
        public override string Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            // 根据JSON令牌类型进行不同的处理
            switch (reader.TokenType)
            {
                // 如果是字符串类型，直接返回字符串值
                case JsonTokenType.String:
                    return reader.GetString();
                // 如果是数字类型，转换为字符串
                case JsonTokenType.Number:
                    // 尝试获取32位整数值
                    if (reader.TryGetInt32(out int intValue))
                        return intValue.ToString(); // 将整数转换为字符串
                    // 尝试获取双精度浮点数值
                    if (reader.TryGetDouble(out double doubleValue))
                        return doubleValue.ToString(); // 将浮点数转换为字符串
                    break;
                // 如果是布尔值true，返回"true"字符串
                case JsonTokenType.True:
                    return "true";
                // 如果是布尔值false，返回"false"字符串
                case JsonTokenType.False:
                    return "false";
                // 如果是null值，返回null
                case JsonTokenType.Null:
                    return null;
            }

            // 默认情况下尝试获取字符串值
            return reader.GetString();
        }

        /// <summary>
        /// 将字符串值写入JSON的方法
        /// </summary>
        /// <param name="writer">UTF8 JSON写入器</param>
        /// <param name="value">要写入的字符串值</param>
        /// <param name="options">JSON序列化选项</param>
        public override void Write(Utf8JsonWriter writer, string value, JsonSerializerOptions options)
        {
            // 将字符串值写入JSON
            writer.WriteStringValue(value);
        }
    }

    /// <summary>
    /// 灵活的整数转换器类
    /// 自定义JSON转换器，将字符串或数字统一转换为整数类型
    /// 用于处理API返回数据类型不一致的问题
    /// </summary>
    public class FlexibleIntConverter : JsonConverter<int>
    {
        /// <summary>
        /// 读取JSON数据并转换为整数的方法
        /// </summary>
        /// <param name="reader">UTF8 JSON读取器</param>
        /// <param name="typeToConvert">要转换的目标类型</param>
        /// <param name="options">JSON序列化选项</param>
        /// <returns>转换后的整数值</returns>
        public override int Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            // 根据JSON令牌类型进行不同的处理
            switch (reader.TokenType)
            {
                // 如果是数字类型，直接返回整数值
                case JsonTokenType.Number:
                    return reader.GetInt32();
                // 如果是字符串类型，尝试解析为整数
                case JsonTokenType.String:
                    var stringValue = reader.GetString(); // 获取字符串值
                    // 尝试将字符串解析为整数
                    if (int.TryParse(stringValue, out int result))
                        return result; // 解析成功，返回整数值
                    return 0; // 解析失败，返回默认值0
                // 如果是null值，返回默认值0
                case JsonTokenType.Null:
                    return 0;
            }

            // 默认情况下尝试获取整数值
            return reader.GetInt32();
        }

        /// <summary>
        /// 将整数值写入JSON的方法
        /// </summary>
        /// <param name="writer">UTF8 JSON写入器</param>
        /// <param name="value">要写入的整数值</param>
        /// <param name="options">JSON序列化选项</param>
        public override void Write(Utf8JsonWriter writer, int value, JsonSerializerOptions options)
        {
            // 将整数值写入JSON
            writer.WriteNumberValue(value);
        }
    }
}
