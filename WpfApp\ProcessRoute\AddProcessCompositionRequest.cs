using System.Text.Json.Serialization;

namespace WpfApp.ProcessRoute
{
    /// <summary>
    /// 新增工序组成请求模型
    /// 用于向后端API发送新增工序组成的数据
    /// </summary>
    public class AddProcessCompositionRequest
    {
        /// <summary>
        /// 工艺路线ID
        /// </summary>
        [JsonPropertyName("processRouteId")]
        public string ProcessRouteId { get; set; }

        /// <summary>
        /// 序列号
        /// </summary>
        [JsonPropertyName("serialCode")]
        public string SerialCode { get; set; }

        /// <summary>
        /// 工艺脚本ID - 改为字符串类型以避免类型转换问题
        /// </summary>
        [JsonPropertyName("processScriptId")]
        public string ProcessScriptId { get; set; }

        /// <summary>
        /// 下一工序ID
        /// </summary>
        [JsonPropertyName("nextProcedure")]
        public int NextProcedure { get; set; }

        /// <summary>
        /// 关系
        /// </summary>
        [JsonPropertyName("relationship")]
        public string Relationship { get; set; }

        /// <summary>
        /// 是否关键工序
        /// </summary>
        [JsonPropertyName("isKeyProcess")]
        public bool IsKeyProcess { get; set; }

        /// <summary>
        /// 准备时间
        /// </summary>
        [JsonPropertyName("preparationTime")]
        public string PreparationTime { get; set; }

        /// <summary>
        /// 等待时间
        /// </summary>
        [JsonPropertyName("waitTime")]
        public string WaitTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [JsonPropertyName("remark")]
        public string Remark { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [JsonPropertyName("status")]
        public bool Status { get; set; } = true;
    }

    /// <summary>
    /// API响应结果模型 - 使用灵活的解析方式
    /// </summary>
    public class AddProcessCompositionResponse
    {
        [JsonPropertyName("code")]
        public int Code { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; }

        [JsonPropertyName("result")]
        public object Result { get; set; }
    }

    /// <summary>
    /// 简化的API响应模型 - 只解析基本字段
    /// </summary>
    public class SimpleApiResponse
    {
        public int Code { get; set; }
        public string Type { get; set; }
        public string Message { get; set; }
    }
}
