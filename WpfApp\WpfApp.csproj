﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <!-- 启用WPF调试工具 -->
    <EnableXamlGeneratedBreakpoints>true</EnableXamlGeneratedBreakpoints>
    <EnableDefaultPageItems>true</EnableDefaultPageItems>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MaterialDesignThemes" Version="5.2.1" />
    <PackageReference Include="MaterialDesignThemes.MahApps" Version="5.2.1" />
    <PackageReference Include="Newtonsoft.Json.Bson" Version="1.0.3" />
  </ItemGroup>

  <ItemGroup>
    <!-- 包含Plan目录下的所有新文件 -->
    <Compile Update="Plan\BoolToYesNoConverter.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Update="Plan\RowNumberConverter.cs">
      <SubType>Code</SubType>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <!-- 移除对不存在的图片的引用 -->
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Procedure\" />
  </ItemGroup>

</Project>
