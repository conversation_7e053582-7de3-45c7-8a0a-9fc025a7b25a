using System.Text.Json.Serialization;
using System.Runtime.CompilerServices;
using System.ComponentModel;

namespace WpfApp.Procedure
{
    /// <summary>
    /// 工序数据模型类
    /// </summary>
    public class ProcessStep : INotifyPropertyChanged
    {
        private bool _isSelected;

        /// <summary>
        /// 是否被选中 - 用于复选框绑定
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                OnPropertyChanged(nameof(IsSelected));
            }
        }

        /// <summary>
        /// 工序步骤ID
        /// </summary>
        [JsonPropertyName("processStepId")]
        public int ProcessStepId { get; set; }

        /// <summary>
        /// 工序名称
        /// </summary>
        [JsonPropertyName("processName")]
        public string ProcessName { get; set; }

        /// <summary>
        /// 工序编码
        /// </summary>
        [JsonPropertyName("processCode")]
        public string ProcessCode { get; set; }

        /// <summary>
        /// 状态 - 布尔值，true表示启用，false表示禁用
        /// </summary>
        [JsonPropertyName("status")]
        public bool Status { get; set; }

        /// <summary>
        /// 工序说明
        /// </summary>
        [JsonPropertyName("description")]
        public string Description { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [JsonPropertyName("remark")]
        public string Remark { get; set; }

        /// <summary>
        /// 记录ID - 保持字符串类型以兼容现有代码
        /// </summary>
        public string Id => ProcessStepId.ToString();

        /// <summary>
        /// 序号 - 用于界面显示，从1开始递增
        /// </summary>
        public int RowNumber { get; set; }

        /// <summary>
        /// 状态显示文本 - 只读属性，将布尔值转换为中文显示
        /// </summary>
        public string StatusDisplay => Status ? "启用" : "禁用";

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
} 
