using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Net.Http;
using System.Runtime.CompilerServices;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Windows.Input;
using WpfApp.Common;

namespace WpfApp.ProcessRoute
{
    /// <summary>
    /// 产品多选对话框ViewModel
    /// </summary>
    public class ProductMultiSelectViewModel : INotifyPropertyChanged
    {
        private readonly HttpClient _httpClient;
        private ObservableCollection<ProductSelectItem> _products;
        private bool _isLoading;
        private int _currentPage = 1;
        private int _pageSize = 20;
        private int _totalPages;
        private int _totalCount;
        private string _searchKeyword = "";

        public event Action<bool, List<ProductSelectItem>> CloseRequested;

        public ProductMultiSelectViewModel()
        {
            _httpClient = new HttpClient();
            Products = new ObservableCollection<ProductSelectItem>();
            
            // 初始化命令
            ConfirmCommand = new RelayCommand(_ => Confirm());
            CancelCommand = new RelayCommand(_ => Cancel());
            SelectAllCommand = new RelayCommand(_ => SelectAll());
            UnselectAllCommand = new RelayCommand(_ => UnselectAll());
            SearchCommand = new RelayCommand(async _ => await SearchAsync());
            PreviousPageCommand = new RelayCommand(_ => PreviousPage(), _ => CanPreviousPage());
            NextPageCommand = new RelayCommand(_ => NextPage(), _ => CanNextPage());
            
            // 加载数据
            _ = LoadProductsAsync();
        }

        #region 属性

        public ObservableCollection<ProductSelectItem> Products
        {
            get => _products;
            set
            {
                _products = value;
                OnPropertyChanged();
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        public int CurrentPage
        {
            get => _currentPage;
            set
            {
                _currentPage = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PageInfo));
            }
        }

        public int TotalPages
        {
            get => _totalPages;
            set
            {
                _totalPages = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PageInfo));
            }
        }

        public int TotalCount
        {
            get => _totalCount;
            set
            {
                _totalCount = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PageInfo));
            }
        }

        public string SearchKeyword
        {
            get => _searchKeyword;
            set
            {
                _searchKeyword = value;
                OnPropertyChanged();
            }
        }

        public string PageInfo => $"第 {CurrentPage} 页，共 {TotalPages} 页，总计 {TotalCount} 条记录";

        public List<ProductSelectItem> SelectedProducts => Products?.Where(p => p.IsSelected).ToList() ?? new List<ProductSelectItem>();

        #endregion

        #region 命令

        public ICommand ConfirmCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand SelectAllCommand { get; }
        public ICommand UnselectAllCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand PreviousPageCommand { get; }
        public ICommand NextPageCommand { get; }

        #endregion

        #region 方法

        private async Task LoadProductsAsync()
        {
            try
            {
                IsLoading = true;

                // 设置认证头
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    _httpClient.DefaultRequestHeaders.Clear();
                    _httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                // 构建API URL
                var url = $"http://localhost:5005/api/bom/pageProduct?Page={CurrentPage}&PageSize={_pageSize}";
                if (!string.IsNullOrWhiteSpace(SearchKeyword))
                {
                    url += $"&keyword={Uri.EscapeDataString(SearchKeyword)}";
                }

                var response = await _httpClient.GetAsync(url);
                var jsonString = await response.Content.ReadAsStringAsync();

                System.Diagnostics.Debug.WriteLine($"产品分页API响应: {jsonString}");

                if (response.IsSuccessStatusCode)
                {
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    };

                    var apiResponse = JsonSerializer.Deserialize<ProductApiResponse>(jsonString, options);

                    if (apiResponse?.Result?.Items != null)
                    {
                        var productItems = new ObservableCollection<ProductSelectItem>();
                        
                        foreach (var item in apiResponse.Result.Items)
                        {
                            productItems.Add(new ProductSelectItem
                            {
                                Id = item.Id ?? "",
                                ProductCode = item.ProductCode ?? "",
                                ProductName = item.ProductName ?? "",
                                Specification = item.Specification ?? "",
                                Unit = item.Unit ?? "",
                                ProductType = item.ProductType ?? "",
                                ProductAttribute = item.ProductAttribute ?? "",
                                IsSelected = false
                            });
                        }

                        Products = productItems;
                        TotalCount = apiResponse.Result.Total;
                        TotalPages = apiResponse.Result.TotalPages;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载产品数据失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task SearchAsync()
        {
            CurrentPage = 1;
            await LoadProductsAsync();
        }

        private void SelectAll()
        {
            foreach (var product in Products)
            {
                product.IsSelected = true;
            }
        }

        private void UnselectAll()
        {
            foreach (var product in Products)
            {
                product.IsSelected = false;
            }
        }

        private async void PreviousPage()
        {
            if (CanPreviousPage())
            {
                CurrentPage--;
                await LoadProductsAsync();
            }
        }

        private async void NextPage()
        {
            if (CanNextPage())
            {
                CurrentPage++;
                await LoadProductsAsync();
            }
        }

        private bool CanPreviousPage() => CurrentPage > 1;
        private bool CanNextPage() => CurrentPage < TotalPages;

        private void Confirm()
        {
            var selectedProducts = SelectedProducts;
            CloseRequested?.Invoke(true, selectedProducts);
        }

        private void Cancel()
        {
            CloseRequested?.Invoke(false, new List<ProductSelectItem>());
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    /// <summary>
    /// 产品选择项数据模型
    /// </summary>
    public class ProductSelectItem : INotifyPropertyChanged
    {
        private bool _isSelected;

        public string Id { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public string Specification { get; set; }
        public string Unit { get; set; }
        public string ProductType { get; set; }
        public string ProductAttribute { get; set; }

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 产品API响应模型
    /// </summary>
    public class ProductApiResponse
    {
        [JsonPropertyName("code")]
        public int Code { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; }

        [JsonPropertyName("result")]
        public ProductPageResult Result { get; set; }
    }

    public class ProductPageResult
    {
        [JsonPropertyName("page")]
        public int Page { get; set; }

        [JsonPropertyName("pageSize")]
        public int PageSize { get; set; }

        [JsonPropertyName("total")]
        public int Total { get; set; }

        [JsonPropertyName("totalPages")]
        public int TotalPages { get; set; }

        [JsonPropertyName("items")]
        public List<ProductApiItem> Items { get; set; }
    }

    public class ProductApiItem
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("productCode")]
        public string ProductCode { get; set; }

        [JsonPropertyName("productName")]
        public string ProductName { get; set; }

        [JsonPropertyName("specification")]
        public string Specification { get; set; }

        [JsonPropertyName("unit")]
        public string Unit { get; set; }

        [JsonPropertyName("productType")]
        public string ProductType { get; set; }

        [JsonPropertyName("productAttribute")]
        public string ProductAttribute { get; set; }
    }
}
