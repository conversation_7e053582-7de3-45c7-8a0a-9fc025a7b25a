# API地址修改总结

## 修改概述

将WPF应用程序的后端API地址从本地地址 `http://localhost:5005` 修改为远程地址 `http://*************:8085`。

## 新增文件

### ApiConfig.cs
创建了统一的API配置类，用于管理所有API地址：

```csharp
namespace WpfApp
{
    public static class ApiConfig
    {
        // 后端API基础地址
        public static string BaseUrl = "http://*************:8085";
        
        // API端点
        public static class Endpoints
        {
            // 认证相关
            public static string Login = "/api/sysAuth/simpleLogin";
            
            // 生产计划相关
            public static string ProductionPlan = "/api/productionPlan/productionPlan";
            public static string ProductionPlanDetail = "/api/productionPlan/productionPlanDetail";
            public static string ProductionPlanPage = "/api/productionPlan/page";
            public static string SourceTypeList = "/api/productionPlan/sourceTypeList";
            public static string DecomposeProductionPlan = "/api/productionPlan/decomposeProductionPlan";
            public static string WithdrawProductionPlan = "/api/productionPlan/withdrawProductionPlan";
            public static string BatchDeleteProductionPlan = "/api/productionPlan/batchDeleteProductionPlan";
            public static string UploadAttachment = "/api/productionPlanUpload/uploadAttachment";
            
            // BOM相关
            public static string Bom = "/api/bom/bom";
            public static string PageBomEntity = "/api/bom/pageBomEntity";
            public static string PageProduct = "/api/bom/pageProduct";
            public static string PageProcessRoute = "/api/bom/pageProcessRoute";
            public static string GetProductByCode = "/api/bom/getProductByCode";
            public static string GetProcessRouteByCode = "/api/bom/getProcessRouteByCode";
            public static string PageProcessComposition = "/api/bom/pageProcessComposition";
            
            // 工单相关
            public static string WorkOrder = "/api/workOrder/workOrder";
            public static string WorkOrderDetail = "/api/workOrder/detail";
            public static string MaterialEntityList = "/api/workOrder/materialEntityList";
            public static string ProductSchedu = "/api/workOrder/productSchedu";
            
            // 任务相关
            public static string Task = "/api/task/task";
            public static string DelTask = "/api/task/delTask";
            public static string UpdTask = "/api/task/updTask";
            public static string Sites = "/api/task/sites";
            public static string TaskSitesProcessRoute = "/api/task/taskSitesProcessRoute";
        }
        
        // 获取完整的API URL
        public static string GetUrl(string endpoint)
        {
            return $"{BaseUrl}{endpoint}";
        }
    }
}
```

## 修改的文件

### 1. MainWindowViewModel.cs
- 修改登录API地址：`http://localhost:5005/api/sysAuth/simpleLogin/{Username}/{Password}` → `{ApiConfig.GetUrl(ApiConfig.Endpoints.Login)}/{Username}/{Password}`

### 2. Plan/ProductSelectionViewModel.cs
- 修改HttpClient基地址：`http://localhost:5005/` → `ApiConfig.BaseUrl + "/"`

### 3. Plan/BomSelectionViewModel.cs
- 修改HttpClient基地址：`http://localhost:5005/` → `ApiConfig.BaseUrl + "/"`

### 4. Bom/MaterialSelectViewModel.cs
- 修改HttpClient基地址：`http://localhost:5005/` → `ApiConfig.BaseUrl + "/"`

### 5. WorkOrder/WorkOrderViewModel.cs
- 修改工单查询API：`http://localhost:5005/api/workOrder/workOrder` → `{ApiConfig.GetUrl(ApiConfig.Endpoints.WorkOrder)}`

### 6. ProductionSchedu/ScheduleViewModel.cs
- 修改删除任务API：`http://localhost:5005/api/task/delTask` → `{ApiConfig.GetUrl(ApiConfig.Endpoints.DelTask)}`
- 修改工单详情API：`http://localhost:5005/api/workOrder/detail` → `{ApiConfig.GetUrl(ApiConfig.Endpoints.WorkOrderDetail)}`
- 修改物料列表API：`http://localhost:5005/api/workOrder/materialEntityList` → `{ApiConfig.GetUrl(ApiConfig.Endpoints.MaterialEntityList)}`
- 修改任务站点工艺路线API：`http://localhost:5005/api/task/taskSitesProcessRoute` → `{ApiConfig.GetUrl(ApiConfig.Endpoints.TaskSitesProcessRoute)}`
- 修改产品排产API：`http://localhost:5005/api/workOrder/productSchedu` → `{ApiConfig.GetUrl(ApiConfig.Endpoints.ProductSchedu)}`

### 7. ProductionSchedu/AddTaskViewModel.cs
- 修改更新任务API：`http://localhost:5005/api/task/updTask` → `{ApiConfig.GetUrl(ApiConfig.Endpoints.UpdTask)}`
- 修改添加任务API：`http://localhost:5005/api/task/task` → `ApiConfig.GetUrl(ApiConfig.Endpoints.Task)`
- 修改获取站点API：`http://localhost:5005/api/task/sites` → `ApiConfig.GetUrl(ApiConfig.Endpoints.Sites)`

### 8. Plan/PlanViewModel.cs
- 修改来源类型列表API：`http://localhost:5005/api/productionPlan/sourceTypeList` → `ApiConfig.GetUrl(ApiConfig.Endpoints.SourceTypeList)`
- 修改生产计划API：`http://localhost:5005/api/productionPlan/productionPlan` → `ApiConfig.GetUrl(ApiConfig.Endpoints.ProductionPlan)`
- 修改文件URL：`http://localhost:5005/{fileUrl}` → `{ApiConfig.BaseUrl}/{fileUrl}`
- 修改上传附件API：`http://localhost:5005/api/productionPlanUpload/uploadAttachment` → `ApiConfig.GetUrl(ApiConfig.Endpoints.UploadAttachment)`
- 修改生产计划分页API：`http://localhost:5005/api/productionPlan/page` → `{ApiConfig.GetUrl(ApiConfig.Endpoints.ProductionPlanPage)}`
- 修改分解计划API：`http://localhost:5005/api/productionPlan/decomposeProductionPlan` → `ApiConfig.GetUrl(ApiConfig.Endpoints.DecomposeProductionPlan)`
- 修改撤回计划API：`http://localhost:5005/api/productionPlan/withdrawProductionPlan` → `ApiConfig.GetUrl(ApiConfig.Endpoints.WithdrawProductionPlan)`
- 修改批量删除计划API：`http://localhost:5005/api/productionPlan/batchDeleteProductionPlan` → `ApiConfig.GetUrl(ApiConfig.Endpoints.BatchDeleteProductionPlan)`

### 9. Plan/EditPlanViewModel.cs
- 修改计划详情API：`http://localhost:5005/api/productionPlan/productionPlanDetail` → `{ApiConfig.GetUrl(ApiConfig.Endpoints.ProductionPlanDetail)}`
- 修改来源类型列表API：`http://localhost:5005/api/productionPlan/sourceTypeList` → `ApiConfig.GetUrl(ApiConfig.Endpoints.SourceTypeList)`
- 修改生产计划API：`http://localhost:5005/api/productionPlan/productionPlan` → `ApiConfig.GetUrl(ApiConfig.Endpoints.ProductionPlan)`
- 修改文件URL：`http://localhost:5005/{fileUrl}` → `{ApiConfig.BaseUrl}/{fileUrl}`
- 修改上传附件API：`http://localhost:5005/api/productionPlanUpload/uploadAttachment` → `ApiConfig.GetUrl(ApiConfig.Endpoints.UploadAttachment)`

### 10. Bom/ProductSelectViewModel.cs
- 修改产品分页API：`http://localhost:5005/api/bom/pageProduct` → `{ApiConfig.GetUrl(ApiConfig.Endpoints.PageProduct)}`

### 11. Bom/ProcessRouteSelectViewModel.cs
- 修改工艺路线分页API：`http://localhost:5005/api/bom/pageProcessRoute` → `{ApiConfig.GetUrl(ApiConfig.Endpoints.PageProcessRoute)}`

### 12. Bom/BomViewModel.cs
- 修改BOM实体分页API：`http://localhost:5005/api/bom/pageBomEntity` → `{ApiConfig.GetUrl(ApiConfig.Endpoints.PageBomEntity)}`

### 13. Bom/BomEditViewModel.cs
- 修改工序组合API：`http://localhost:5005/api/bom/pageProcessComposition/1/11/{processRouteCode}` → `{ApiConfig.GetUrl(ApiConfig.Endpoints.PageProcessComposition)}/1/11/{processRouteCode}`
- 修改BOM保存API：`http://localhost:5005/api/bom/bom` → `ApiConfig.GetUrl(ApiConfig.Endpoints.Bom)`

## 修改方式

所有修改都采用了以下方式：

1. **使用配置类**：通过 `ApiConfig.GetUrl(ApiConfig.Endpoints.XXX)` 的方式获取完整URL
2. **统一管理**：所有API端点都在 `ApiConfig.Endpoints` 类中定义
3. **易于维护**：如果需要修改API地址，只需要修改 `ApiConfig.BaseUrl` 即可

## 优势

1. **集中管理**：所有API地址都在一个地方管理，便于维护
2. **类型安全**：使用强类型的端点定义，避免字符串拼写错误
3. **易于切换**：可以轻松在不同环境间切换API地址
4. **代码清晰**：使用有意义的端点名称，提高代码可读性

## 测试建议

修改完成后，建议进行以下测试：

1. **登录功能**：测试用户登录是否正常
2. **数据加载**：测试各个页面的数据加载是否正常
3. **数据提交**：测试新增、编辑、删除等操作是否正常
4. **文件上传**：测试附件上传功能是否正常
5. **网络连接**：确保网络能够正常访问远程服务器

## 注意事项

1. **网络连接**：确保客户端能够访问 `http://*************:8085`
2. **防火墙设置**：确保8085端口没有被防火墙阻止
3. **服务器状态**：确保远程服务器正常运行
4. **认证令牌**：确保认证机制在远程服务器上正常工作 